import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

export const getUserLevel = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const userLevel = await ctx.db
      .query('user_levels')
      .withIndex('by_user', q => q.eq('user_id', args.userId))
      .first();

    return (
      userLevel ?? {
        level: 1,
        current_xp: 0,
        total_xp: 0,
      }
    );
  },
});

export const addExperience = mutation({
  args: {
    userId: v.string(),
    xpAmount: v.number(),
  },
  handler: async (ctx, args) => {
    const userLevel = await ctx.db
      .query('user_levels')
      .withIndex('by_user', q => q.eq('user_id', args.userId))
      .first();

    if (!userLevel) {
      // Create new user level entry
      return ctx.db.insert('user_levels', {
        user_id: args.userId,
        level: 1,
        current_xp: args.xpAmount,
        total_xp: args.xpAmount,
        last_updated: Date.now(),
      });
    }

    const calculateRequiredXP = (level: number) =>
      Math.floor(level * 2.5 * 100);
    const requiredXP = calculateRequiredXP(userLevel.level);
    const newCurrentXP = userLevel.current_xp + args.xpAmount;
    const newTotalXP = userLevel.total_xp + args.xpAmount;

    let newLevel = userLevel.level;
    let remainingXP = newCurrentXP;

    // Check for level ups
    while (remainingXP >= calculateRequiredXP(newLevel)) {
      remainingXP -= calculateRequiredXP(newLevel);
      newLevel++;
    }

    return ctx.db.patch(userLevel._id, {
      level: newLevel,
      current_xp: remainingXP,
      total_xp: newTotalXP,
      last_updated: Date.now(),
    });
  },
});
