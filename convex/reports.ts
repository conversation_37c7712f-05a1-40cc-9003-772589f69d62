import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

export const getReports = query({
  args: {},
  handler: async ctx => {
    const reports = await ctx.db.query('reports').collect();

    return reports;
  },
});

export const saveReport = mutation({
  args: {
    content: v.string(),
    user_id: v.string(),
    email: v.string(),
    lesson_id: v.id('lessons'),
  },
  handler: async (ctx, args) => {
    const reportId = await ctx.db.insert('reports', {
      ...args,
    });

    return reportId;
  },
});
