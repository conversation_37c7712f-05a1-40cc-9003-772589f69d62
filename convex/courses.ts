import { v } from 'convex/values';

import { query } from './_generated/server';

export const getCourses = query({
  handler: async ctx => await ctx.db.query('courses').collect(),
});

export const getCourse = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const response = await ctx.db
      .query('courses')
      .filter(q => q.eq(q.field('slug'), args.slug))
      .first();

    return response;
  },
});

export const listCourses = query({
  args: {},
  handler: async ctx => await ctx.db.query('courses').collect(),
});
