import { v } from 'convex/values';
import { mutation, query } from './_generated/server';

export const getUserProgress = query({
  args: {
    user_id: v.string(),
    course_id: v.id('courses'),
  },
  handler: async (ctx, args) => {
    const progress = await ctx.db
      .query('user_progress')
      .withIndex('by_user_and_course', q =>
        q.eq('user_id', args.user_id).eq('course_id', args.course_id)
      )
      .collect();

    return progress;
  },
});

export const updateLessonProgress = mutation({
  args: {
    user_id: v.string(),
    lesson_id: v.id('lessons'),
    course_id: v.id('courses'),
    is_completed: v.boolean(),
  },
  handler: async (ctx, args) => {
    const existingProgress = await ctx.db
      .query('user_progress')
      .withIndex('by_user_and_lesson', q =>
        q.eq('user_id', args.user_id).eq('lesson_id', args.lesson_id)
      )
      .first();

    if (existingProgress) {
      return await ctx.db.patch(existingProgress._id, {
        is_completed: args.is_completed,
        completed_at: Date.now(),
      });
    }

    return await ctx.db.insert('user_progress', {
      user_id: args.user_id,
      lesson_id: args.lesson_id,
      course_id: args.course_id,
      is_completed: args.is_completed,
      completed_at: Date.now(),
    });
  },
});
