import { v } from 'convex/values';
import { mutation, query } from './_generated/server';

export const getUnreadNotifications = query({
  args: { user_id: v.string() },
  handler: async (ctx, args) => {
    const notifications = await ctx.db
      .query('notifications')
      .withIndex('by_user_and_read', (q) =>
        q.eq('user_id', args.user_id).eq('read', false)
      )
      .order('desc')
      .collect();

    return notifications;
  },
});

export const markNotificationAsRead = mutation({
  args: { notification_id: v.id('notifications') },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.notification_id, { read: true });
  },
});

export const createNotification = mutation({
  args: {
    user_id: v.string(),
    title: v.string(),
    content: v.string(),
    type: v.union(
      v.literal('achievement'),
      v.literal('course'),
      v.literal('system')
    ),
    action_url: v.optional(v.string()),
    related_id: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert('notifications', {
      ...args,
      read: false,
      created_at: Date.now(),
    });
  },
});