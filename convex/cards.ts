import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

export const getCards = query({
  args: {},
  handler: async ctx => {
    return await ctx.db.query('collectible_cards').collect();
  },
});

export const getUserCards = query({
  args: {
    user_id: v.string(),
  },
  handler: async (ctx, args) => {
    const userCards = await ctx.db
      .query('user_cards')
      .filter(q => q.eq(q.field('user_id'), args.user_id))
      .collect();

    const cardIds = userCards.map(uc => uc.card_id);
    const cards = await Promise.all(cardIds.map(id => ctx.db.get(id)));

    return cards.filter(
      (card): card is NonNullable<typeof card> => card !== null
    );
  },
});

export const updateCardBackground = mutation({
  args: {
    cardId: v.id('collectible_cards'),
    backgroundImage: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.cardId, {
      background_image: args.backgroundImage,
    });
  },
});

export const updateCardImage = mutation({
  args: {
    cardId: v.id('collectible_cards'),
    imagePath: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.cardId, {
      background_image: args.imagePath,
    });
  },
});
