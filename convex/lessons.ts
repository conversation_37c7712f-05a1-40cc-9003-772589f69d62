import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

export const getLessonContent = query({
  args: {
    course: v.string(),
    lesson: v.string(),
  },
  handler: async (ctx, args) => {
    const course = await ctx.db
      .query('courses')
      .filter(q => q.eq(q.field('slug'), args.course))
      .first();

    if (!course) return null;

    return await ctx.db
      .query('lessons')
      .withIndex('by_slug_and_course', q =>
        q.eq('slug', args.lesson).eq('course_id', course?._id)
      )
      .first();
  },
});

export const getCourseLessons = query({
  args: {
    course_id: v.id('courses'),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db
      .query('lessons')
      .filter(q => q.eq(q.field('course_id'), args.course_id))
      .collect();

    return result.sort((a, b) => b.order - a.order);
  },
});

export const createLesson = mutation({
  args: {
    _id: v.string(),
    initial_code: v.string(),
    is_published: v.boolean(),
    language: v.string(),
    lesson_content: v.string(),
    slug: v.string(),
    name: v.string(),
    solution_code: v.string(),
    solution_content: v.string(),
    chapter_id: v.id('chapters'),
    course_id: v.id('courses'),
  },
  handler: async (ctx, args) => {
    const order = await ctx.db
      .query('lessons')
      .collect()
      .then(lessons => lessons.length + 1);

    const { _id, ...rest } = args;

    return await ctx.db.insert('lessons', {
      ...rest,
      order,
    });
  },
});

export const patchLesson = mutation({
  args: {
    _id: v.id('lessons'),
    chapter_id: v.optional(v.id('chapters')),
    course_id: v.optional(v.id('courses')),
    name: v.optional(v.string()),
    order: v.optional(v.number()),
    initial_code: v.optional(v.string()),
    is_published: v.optional(v.boolean()),
    language: v.optional(v.string()),
    lesson_content: v.optional(v.string()),
    slug: v.optional(v.string()),
    solution_code: v.optional(v.string()),
    solution_content: v.optional(v.string()),
    _creationTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { _id, _creationTime, ...rest } = args;

    const lesson = await ctx.db.patch(_id, { ...rest });

    return lesson;
  },
});

export const updateLessonOrder = mutation({
  args: {
    lessons: v.array(
      v.object({
        _id: v.id('lessons'),
        order: v.number(),
        chapter_id: v.id('chapters'),
      })
    ),
  },
  handler: async (ctx, args) => {
    await Promise.all(
      args.lessons.map(async ({ _id, order, chapter_id }) => {
        return await ctx.db.patch(_id, {
          order,
          chapter_id,
        });
      })
    );

    return true;
  },
});

export const reorderLesson = mutation({
  args: {
    lesson_id: v.id('lessons'),
    target_chapter_id: v.id('chapters'),
    new_order: v.number(),
  },
  handler: async (ctx, args) => {
    const { lesson_id, target_chapter_id, new_order } = args;

    // Get all lessons in the target chapter
    const chaptersLessons = await ctx.db
      .query('lessons')
      .filter(q => q.eq(q.field('chapter_id'), target_chapter_id))
      .collect();

    // Sort lessons by current order
    const sortedLessons = chaptersLessons.sort((a, b) => a.order - b.order);

    // Find the lesson being moved
    const movingLesson = sortedLessons.find(l => l._id === lesson_id);

    if (!movingLesson) return false;

    // Determine if we're moving up or down
    const isMovingUp = new_order < movingLesson.order;

    const prevLesson = sortedLessons.find(
      l => l.order === new_order && l._id !== lesson_id
    );

    if (prevLesson) {
      if (isMovingUp) {
        await ctx.db.patch(prevLesson._id, {
          order: prevLesson.order + 1,
        });
      } else {
        await ctx.db.patch(prevLesson._id, {
          order: prevLesson.order - 1,
        });
      }

      await ctx.db.patch(lesson_id, {
        order: new_order,
      });
    }
    // const updatesPromises = sortedLessons.map(async lesson => {
    //   if (lesson._id === lesson_id) {
    //     // Update the moved lesson
    //     await ctx.db.patch(lesson_id, {
    //       order: new_order,
    //       chapter_id: target_chapter_id,
    //     });

    //     return true;
    //   } else if (isMovingUp) {
    //     // When moving up, increment order of lessons between new position and old position
    //     if (lesson.order >= new_order && lesson.order < movingLesson.order) {
    //       await ctx.db.patch(lesson._id, {
    //         order: lesson.order + 1,
    //       });

    //       return true;
    //     }
    //   } else {
    //     // When moving down, decrement order of lessons between old position and new position
    //     if (lesson.order <= new_order && lesson.order > movingLesson.order) {
    //       await ctx.db.patch(lesson._id, {
    //         order: lesson.order - 1,
    //       });

    //       return true;
    //     }
    //   }
    // });

    // const updates = await Promise.all(updatesPromises.filter(Boolean));

    // return updates;
  },
});

export const deleteLesson = mutation({
  args: {
    lesson_id: v.id('lessons'),
  },
  handler: async (ctx, args) => {
    // Delete any associated user progress
    const userProgress = await ctx.db
      .query('user_progress')
      .filter(q => q.eq(q.field('lesson_id'), args.lesson_id))
      .collect();

    await Promise.all(
      userProgress.map(progress => ctx.db.delete(progress._id))
    );

    // Delete any associated reports
    const reports = await ctx.db
      .query('reports')
      .filter(q => q.eq(q.field('lesson_id'), args.lesson_id))
      .collect();

    await Promise.all(reports.map(report => ctx.db.delete(report._id)));

    // Delete the lesson itself
    await ctx.db.delete(args.lesson_id);

    return { success: true };
  },
});
