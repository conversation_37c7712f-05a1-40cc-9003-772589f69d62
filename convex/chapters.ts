import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

export const getChapter = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const response = await ctx.db
      .query('chapters')
      .filter(q => q.eq(q.field('slug'), args.slug))
      .first();

    return response;
  },
});

export const getChapterById = query({
  args: {
    chapter_id: v.id('chapters'),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.chapter_id);
  },
});

export const getChapters = query({
  args: {
    course_id: v.id('courses'),
  },
  handler: async (ctx, args) => {
    const result = await ctx.db
      .query('chapters')
      .filter(q => q.eq(q.field('course_id'), args.course_id))
      .collect();

    return result.sort((a, b) => a.order - b.order);
  },
});

export const createChapter = mutation({
  args: {
    name: v.string(),
    course_id: v.id('courses'),
    order: v.number(),
  },
  handler: async (ctx, args) => {
    // Generate a URL-friendly slug from the chapter name
    const slug = args.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    const chapter = await ctx.db.insert('chapters', {
      name: args.name,
      course_id: args.course_id,
      order: args.order,
      slug: slug,
    });

    return chapter;
  },
});

export const deleteChapter = mutation({
  args: {
    chapter_id: v.id('chapters'),
  },
  handler: async (ctx, args) => {
    // First, get all lessons in this chapter
    const chapterLessons = await ctx.db
      .query('lessons')
      .filter(q => q.eq(q.field('chapter_id'), args.chapter_id))
      .collect();

    // Remove chapter_id from all lessons in the chapter
    await Promise.all(
      chapterLessons.map(lesson =>
        ctx.db.patch(lesson._id, { chapter_id: undefined })
      )
    );

    // Delete the chapter itself
    await ctx.db.delete(args.chapter_id);

    return { success: true };
  },
});

export const updateChapterOrder = mutation({
  args: {
    chapters: v.array(
      v.object({
        _id: v.id('chapters'),
        order: v.number(),
      })
    ),
  },
  handler: async (ctx, args) => {
    const updates = await Promise.all(
      args.chapters.map(
        async ({ _id, order }) => await ctx.db.patch(_id, { order })
      )
    );

    return updates;
  },
});

export const updateChapter = mutation({
  args: {
    chapter_id: v.id('chapters'),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Generate a URL-friendly slug from the chapter name
    const slug = args.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    const chapter = await ctx.db.patch(args.chapter_id, {
      name: args.name,
      slug: slug,
    });

    return chapter;
  },
});
