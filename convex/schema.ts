import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // USER PROGRESS
  user_progress: defineTable({
    user_id: v.string(),
    lesson_id: v.id('lessons'),
    course_id: v.id('courses'),
    completed_at: v.number(),
    is_completed: v.boolean(),
  })
    .index('by_user_and_course', ['user_id', 'course_id'])
    .index('by_user_and_lesson', ['user_id', 'lesson_id']),

  // REPORTS
  reports: defineTable({
    content: v.string(),
    email: v.string(),
    lesson_id: v.string(), // correct to lesson id later
    user_id: v.string(),
  }),

  // LANGUAGES
  languages: defineTable({
    language: v.string(),
  }),

  // COURSES
  courses: defineTable({
    name: v.string(),
    slug: v.string(),
    language: v.optional(v.string()),
  }).index('by_slug', ['slug']),

  // CHAPTERS
  chapters: defineTable({
    order: v.number(),
    name: v.string(),
    slug: v.string(),
    course_id: v.id('courses'),
  }).index('by_slug', ['slug']),

  // LESSONS
  lessons: defineTable({
    order: v.number(),
    name: v.string(),
    initial_code: v.string(),
    is_published: v.boolean(),
    language: v.string(),
    course_id: v.id('courses'),
    lesson_content: v.string(),
    slug: v.string(),
    solution_code: v.string(),
    solution_content: v.string(),
    chapter_id: v.optional(v.id('chapters')),
  }).index('by_slug_and_course', ['slug', 'course_id']),

  // ACHIEVEMENTS
  achievements: defineTable({
    name: v.string(),
    description: v.string(),
    icon: v.string(),
    points: v.number(),
    requirement_type: v.string(), // e.g., 'lessons_completed', 'points_earned'
    requirement_value: v.number(),
    badge_image: v.string(),
  }),

  // USER ACHIEVEMENTS
  user_achievements: defineTable({
    user_id: v.string(),
    achievement_id: v.id('achievements'),
    unlocked_at: v.number(),
  }).index('by_user', ['user_id']),

  // COLLECTIBLE CARDS
  collectible_cards: defineTable({
    name: v.string(),
    description: v.string(),
    rarity: v.union(
      v.literal('legendary'),
      v.literal('epic'),
      v.literal('rare'),
      v.literal('common')
    ),
    image_url: v.string(),
    background_image: v.optional(v.string()),
    card_type: v.optional(v.string()), // python_concept, sql_concept, integration
    variation: v.optional(
      v.union(
        v.literal('holographic'),
        v.literal('golden'),
        v.literal('dark'),
        v.literal('rainbow'),
        v.literal('standard'),
        v.literal('frost'),
        v.literal('metallic')
      )
    ),
    stats: v.object({
      power: v.number(),
      difficulty: v.number(),
      popularity: v.number(),
    }),
    details: v.object({
      concepts: v.array(v.string()),
      tips: v.array(v.string()),
      resources: v.array(v.string()),
    }),
  }).index('by_card_type', ['card_type']),

  // USER CARDS COLLECTION
  user_cards: defineTable({
    user_id: v.string(),
    card_id: v.id('collectible_cards'),
    acquired_at: v.number(),
    favorite: v.optional(v.boolean()),
  })
    .index('by_user', ['user_id'])
    .index('by_user_and_favorite', ['user_id', 'favorite']),

  // NOTIFICATIONS
  notifications: defineTable({
    user_id: v.string(),
    title: v.string(),
    content: v.string(),
    type: v.union(
      v.literal('achievement'),
      v.literal('course'),
      v.literal('system')
    ),
    read: v.boolean(),
    created_at: v.number(),
    action_url: v.optional(v.string()),
    related_id: v.optional(v.string()),
  }).index('by_user_and_read', ['user_id', 'read']),

  // USER LEVELS
  user_levels: defineTable({
    user_id: v.string(),
    level: v.number(),
    current_xp: v.number(),
    total_xp: v.number(),
    last_updated: v.number(),
  }).index('by_user', ['user_id']),
});
