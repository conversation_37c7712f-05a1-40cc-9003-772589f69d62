import { v } from 'convex/values';

import { mutation, query } from './_generated/server';

// ==================== QUERIES ====================

// Get all achievements
export const getAchievements = query({
  args: {},
  handler: async ctx => {
    const achievements = await ctx.db
      .query('achievements')
      .order('desc')
      .collect();
    return achievements;
  },
});

// Get achievements for a specific user
export const getUserAchievements = query({
  args: {
    user_id: v.string(),
  },
  handler: async (ctx, args) => {
    const userAchievements = await ctx.db
      .query('user_achievements')
      .filter(q => q.eq(q.field('user_id'), args.user_id))
      .collect();

    // Return just the achievement IDs that the user has unlocked
    return userAchievements.map(ua => ua.achievement_id);
  },
});

// Get user achievements with full details including unlock dates
export const getUserAchievementsWithDetails = query({
  args: {
    user_id: v.string(),
  },
  handler: async (ctx, args) => {
    const userAchievements = await ctx.db
      .query('user_achievements')
      .filter(q => q.eq(q.field('user_id'), args.user_id))
      .collect();

    const achievementsWithDetails = await Promise.all(
      userAchievements.map(async (ua) => {
        const achievement = await ctx.db.get(ua.achievement_id);
        return {
          ...achievement,
          unlocked_at: ua.unlocked_at,
          user_achievement_id: ua._id,
        };
      })
    );

    return achievementsWithDetails.filter(a => a !== null);
  },
});

// Get user's total achievement points
export const getUserPoints = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const userAchievements = await ctx.db
      .query('user_achievements')
      .filter(q => q.eq(q.field('user_id'), args.userId))
      .collect();

    let totalPoints = 0;
    for (const ua of userAchievements) {
      const achievement = await ctx.db.get(ua.achievement_id);
      if (achievement) {
        totalPoints += achievement.points;
      }
    }

    return totalPoints;
  },
});

// Get user's progress for a specific achievement type
export const getAchievementProgress = query({
  args: {
    userId: v.string(),
    requirementType: v.string(),
  },
  handler: async (ctx, args) => {
    // This is a placeholder implementation
    // In a real application, you would track user actions
    // and calculate progress based on those actions
    return 0;
  },
});

// Get achievement statistics
export const getAchievementStats = query({
  args: {
    achievementId: v.id('achievements'),
  },
  handler: async (ctx, args) => {
    const totalUsers = await ctx.db
      .query('user_achievements')
      .filter(q => q.eq(q.field('achievement_id'), args.achievementId))
      .collect();

    return {
      totalUnlocks: totalUsers.length,
    };
  },
});

// ==================== MUTATIONS ====================

// Unlock an achievement for a user
export const unlockAchievement = mutation({
  args: {
    userId: v.string(),
    achievementId: v.id('achievements'),
  },
  handler: async (ctx, args) => {
    // Check if user already has this achievement
    const existing = await ctx.db
      .query('user_achievements')
      .filter(q =>
        q.and(
          q.eq(q.field('user_id'), args.userId),
          q.eq(q.field('achievement_id'), args.achievementId)
        )
      )
      .first();

    if (existing) {
      return { success: false, message: 'Achievement already unlocked' };
    }

    // Add the achievement
    await ctx.db.insert('user_achievements', {
      user_id: args.userId,
      achievement_id: args.achievementId,
      unlocked_at: Date.now(),
    });

    return { success: true, message: 'Achievement unlocked!' };
  },
});

// Track progress towards an achievement
export const trackAchievementProgress = mutation({
  args: {
    userId: v.string(),
    requirementType: v.string(),
    progress: v.number(),
  },
  handler: async (ctx, args) => {
    // Get all achievements of this type
    const achievements = await ctx.db
      .query('achievements')
      .filter(q => q.eq(q.field('requirement_type'), args.requirementType))
      .collect();

    const results = [];

    // Check each achievement to see if it should be unlocked
    for (const achievement of achievements) {
      if (args.progress >= achievement.requirement_value) {
        // Try to unlock the achievement
        const result = await ctx.db
          .query('user_achievements')
          .filter(q =>
            q.and(
              q.eq(q.field('user_id'), args.userId),
              q.eq(q.field('achievement_id'), achievement._id)
            )
          )
          .first();

        if (!result) {
          // Unlock the achievement
          await ctx.db.insert('user_achievements', {
            user_id: args.userId,
            achievement_id: achievement._id,
            unlocked_at: Date.now(),
          });

          results.push({
            achievement: achievement.name,
            points: achievement.points,
            unlocked: true,
          });
        }
      }
    }

    return results;
  },
});

// Reset user achievements (for testing/development)
export const resetUserAchievements = mutation({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const userAchievements = await ctx.db
      .query('user_achievements')
      .filter(q => q.eq(q.field('user_id'), args.userId))
      .collect();

    for (const ua of userAchievements) {
      await ctx.db.delete(ua._id);
    }

    return { success: true, message: 'Achievements reset successfully' };
  },
});
