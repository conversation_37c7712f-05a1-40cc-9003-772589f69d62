import { expect, test } from '@playwright/test';

test.describe('Courses Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/courses');
    // Wait for the main content to load
    await page.waitForSelector('h1');
  });

  test('should display the courses page correctly', async ({ page }) => {
    // Check main elements
    await expect(
      page.getByRole('heading', { name: 'Available Courses' })
    ).toBeVisible();
    await expect(
      page.getByText('Choose from our selection of programming courses')
    ).toBeVisible();
  });

  test('should display course cards with correct information', async ({
    page,
  }) => {
    // Get all course cards
    const courseCards = page.locator('[data-testid="course-card"]');

    // Ensure we have at least one course
    await expect(courseCards).toHaveCount(1);

    // Check first course card structure
    const firstCard = courseCards.first();
    await expect(firstCard.getByTestId('course-icon')).toBeVisible();
    await expect(firstCard.getByTestId('course-progress')).toBeVisible();
    await expect(firstCard.getByTestId('course-title')).toBeVisible();
  });

  test('should navigate to course page when clicking a course card', async ({
    page,
  }) => {
    // Click the first course card
    const firstCourseCard = page.locator('[data-testid="course-card"]').first();
    const href = await firstCourseCard.getAttribute('href');
    await firstCourseCard.click();

    // Verify navigation
    await expect(page).toHaveURL(new RegExp(href as string));
  });

  test('should handle theme correctly', async ({ page }) => {
    // Toggle dark mode
    await page.getByRole('button', { name: /toggle theme/i }).click();

    // Wait for theme change
    await page.waitForTimeout(100);

    // Verify dark mode is applied
    const isDark = await page.evaluate(() =>
      document.documentElement.classList.contains('dark')
    );
    expect(isDark).toBe(true);

    // Verify dark mode styles are applied
    await expect(page.locator('body')).toHaveClass(/dark/);
  });
});
