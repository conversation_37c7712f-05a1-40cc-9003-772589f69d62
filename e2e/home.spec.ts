import { expect, test } from '@playwright/test';

test.describe('Home Page', () => {
  test('should display the home page correctly for unauthenticated users', async ({
    page,
  }) => {
    await page.goto('/');

    // Check if main banner is visible for unauthenticated users
    const banner = page.getByRole('banner');
    await expect(banner).toBeVisible();

    // Check welcome message
    await expect(page.getByText(/Welcome to/)).toBeVisible();
  });

  test('should have working theme toggle', async ({ page }) => {
    await page.goto('/');

    // Find and click theme toggle
    const themeToggle = page.getByRole('button', { name: /toggle theme/i });
    await expect(themeToggle).toBeVisible();

    // Get initial theme
    const initialIsDark = await page.evaluate(() =>
      document.documentElement.classList.contains('dark')
    );

    // Click theme toggle
    await themeToggle.click();

    // Wait for theme change to take effect
    await page.waitForTimeout(100);

    // Verify theme changed
    const newIsDark = await page.evaluate(() =>
      document.documentElement.classList.contains('dark')
    );
    expect(newIsDark).not.toBe(initialIsDark);
  });
});
