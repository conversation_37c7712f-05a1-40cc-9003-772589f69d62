import { expect, test } from '@playwright/test';

test.describe('Code Editor', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/intro-to-python/intro');
    // Wait for editor to be fully loaded
    await page.waitForSelector('.cm-editor');
    // Wait for Pyodide loading to complete
    await page.waitForSelector('.animate-pulse', { state: 'hidden' });
  });

  test('should handle code execution', async ({ page }) => {
    // Clear and type new code
    await page.keyboard.press('Control+A');
    await page.keyboard.press('Delete');
    await page.keyboard.type('print("Hello World")');

    // Click run button
    await page.getByRole('button', { name: /run/i }).click();

    // Wait for execution
    await page.waitForTimeout(1000);

    // Check output
    const output = await page.getByRole('log').textContent();
    expect(output).toContain('Hello World');
  });

  test('should show error messages for invalid code', async ({ page }) => {
    // Type invalid code
    await page.keyboard.press('Control+A');
    await page.keyboard.press('Delete');
    await page.keyboard.type('print(undefined_variable)');

    // Run code
    await page.getByRole('button', { name: /run/i }).click();

    // Wait for execution
    await page.waitForTimeout(1000);

    // Check for error message
    const output = await page.getByRole('log').textContent();
    expect(output).toContain('NameError');
  });

  test('should be able to show solution', async ({ page }) => {
    // Click solution button
    await page.getByRole('button', { name: /solution/i }).click();

    // Verify solution is shown
    await expect(page.locator('.cm-merge')).toBeVisible();
  });

  test('should be able to reset code', async ({ page }) => {
    // Get initial code
    const initialCode = await page.locator('.cm-editor').textContent();

    // Change code
    await page.keyboard.press('Control+A');
    await page.keyboard.press('Delete');
    await page.keyboard.type('print("New code")');

    // Click reset button
    await page.getByRole('button', { name: /reset/i }).click();

    // Wait for reset
    await page.waitForTimeout(100);

    // Verify code was reset
    const resetCode = await page.locator('.cm-editor').textContent();
    expect(resetCode?.trim()).toBe(initialCode?.trim());
  });

  test('should persist editor theme with system theme', async ({ page }) => {
    // Toggle theme
    await page.getByRole('button', { name: /toggle theme/i }).click();

    // Wait for theme change
    await page.waitForTimeout(100);

    // Check if editor theme changed
    const isDark = await page.evaluate(() =>
      document.documentElement.classList.contains('dark')
    );

    if (isDark) {
      await expect(page.locator('.cm-editor')).toHaveClass(/nord/);
    } else {
      await expect(page.locator('.cm-editor')).toHaveClass(/github-light/);
    }
  });
});
