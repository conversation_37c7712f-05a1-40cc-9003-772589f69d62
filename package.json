{"name": "code-playground-next", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm i && concurrently \"set NODE_OPTIONS\"--inspect\"\" \"next dev -p 5789\" \"convex dev\"", "turbo:dev": "set NODE_OPTIONS='--inspect' && pnpm i && next dev -p 5789 --turbopack", "db": "convex dev", "build": "next build", "start": "next start -p 5789", "lint": "next lint"}, "dependencies": {"@clerk/clerk-react": "^5.33.0", "@clerk/nextjs": "^6.24.0", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.5", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@replit/codemirror-lang-csharp": "^6.2.0", "@tailwindcss/container-queries": "^0.1.1", "@types/mdx": "^2.0.13", "@uidotdev/usehooks": "^2.4.1", "@uiw/codemirror-theme-dracula": "^4.24.0", "@uiw/codemirror-theme-github": "^4.24.0", "@uiw/codemirror-theme-gruvbox-dark": "^4.24.0", "@uiw/codemirror-theme-nord": "^4.24.0", "@uiw/codemirror-themes": "^4.24.0", "@uiw/react-codemirror": "^4.24.0", "@uiw/react-markdown-preview": "^5.1.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "codemirror": "^6.0.2", "concurrently": "^9.2.0", "convex": "^1.25.2", "convex-helpers": "^0.1.99", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.0", "highlight.js": "^11.11.1", "html-react-parser": "^5.2.5", "immer": "^10.1.1", "input-otp": "^1.4.2", "isomorphic-fetch": "^3.0.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "radash": "^12.1.1", "react": "^19.1.0", "react-codemirror-merge": "^4.24.0", "react-day-picker": "9.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "react-scan": "^0.4.3", "recharts": "^3.1.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@next/eslint-plugin-next": "^15.3.5", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "engines": {"node": ">=22.11.0", "npm": "11.0.0"}, "packageManager": "pnpm@10.13.1", "pnpm": {"onlyBuiltDependencies": ["@clerk/shared", "@tailwindcss/oxide", "@vercel/speed-insights", "esbuild", "sharp", "unrs-resolver"]}}