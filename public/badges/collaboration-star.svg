<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCD4"/>
      <stop offset="100%" style="stop-color:#006064"/>
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- People Icons -->
  <g fill="#FFFFFF">
    <!-- Center Person -->
    <circle cx="100" cy="90" r="15"/>
    <path d="M80 140c0-20 40-20 40 0"/>
    
    <!-- Left Person -->
    <circle cx="60" cy="100" r="12"/>
    <path d="M45 140c0-15 30-15 30 0"/>
    
    <!-- Right Person -->
    <circle cx="140" cy="100" r="12"/>
    <path d="M125 140c0-15 30-15 30 0"/>
  </g>
  
  <!-- Connection Lines -->
  <g stroke="#FFFFFF" stroke-width="2">
    <line x1="75" y1="95" x2="85" y2="90"/>
    <line x1="115" y1="90" x2="125" y2="95"/>
    <line x1="100" y1="110" x2="100" y2="120"/>
  </g>
</svg>
