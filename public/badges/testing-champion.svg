<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0"/>
      <stop offset="100%" style="stop-color:#6A1B9A"/>
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Test Tube -->
  <path d="M90 40h20v80l-10 30-10-30z" fill="#FFFFFF"/>
  <path d="M90 90h20v30l-10 30-10-30z" fill="#E91E63" opacity="0.8"/>
  
  <!-- Checkmarks -->
  <g stroke="#FFFFFF" stroke-width="4" fill="none">
    <path d="M140 60l-8 8-4-4"/>
    <path d="M140 80l-8 8-4-4"/>
    <path d="M140 100l-8 8-4-4"/>
  </g>
  
  <!-- Bubbles -->
  <circle cx="100" cy="100" r="3" fill="#FFFFFF"/>
  <circle cx="95" cy="110" r="2" fill="#FFFFFF"/>
  <circle cx="105" cy="105" r="2" fill="#FFFFFF"/>
</svg>
