<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50"/>
      <stop offset="100%" style="stop-color:#2E7D32"/>
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Lightbulb Icon -->
  <path d="M100 30c-25 0-45 20-45 45 0 20 13 37 30 43v12h30v-12c17-6 30-23 30-43 0-25-20-45-45-45zm-10 110h20v10h-20v-10zm10-90c-14 0-25 11-25 25 0 11 7 21 17 24v16h16v-16c10-3 17-13 17-24 0-14-11-25-25-25z" 
        fill="#FFFFFF"/>
  
  <!-- Gear Elements -->
  <circle cx="100" cy="75" r="5" fill="#FFFFFF"/>
  <path d="M85 90l30-30M85 60l30 30" stroke="#FFFFFF" stroke-width="4"/>
</svg>
