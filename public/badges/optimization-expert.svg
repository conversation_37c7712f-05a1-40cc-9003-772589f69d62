<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722"/>
      <stop offset="100%" style="stop-color:#D84315"/>
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Speedometer -->
  <path d="M50 130a60 60 0 1 1 100 0" fill="none" stroke="#FFFFFF" stroke-width="8"/>
  
  <!-- Dial Lines -->
  <g stroke="#FFFFFF" stroke-width="2">
    <line x1="60" y1="120" x2="65" y2="115"/>
    <line x1="80" y1="100" x2="85" y2="95"/>
    <line x1="100" y1="90" x2="100" y2="85"/>
    <line x1="120" y1="100" x2="115" y2="95"/>
    <line x1="140" y1="120" x2="135" y2="115"/>
  </g>
  
  <!-- Needle -->
  <path d="M100 130L120 110 100 70 80 110z" fill="#FFFFFF"/>
  <circle cx="100" cy="130" r="10" fill="#FFFFFF"/>
</svg>
