<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3"/>
      <stop offset="100%" style="stop-color:#1565C0"/>
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Code Review Icon -->
  <g fill="#FFFFFF">
    <!-- Document -->
    <path d="M70 40h60v100H70z" fill="#FFFFFF" opacity="0.9"/>
    <!-- Code Lines -->
    <rect x="80" y="60" width="40" height="4" rx="2"/>
    <rect x="80" y="70" width="30" height="4" rx="2"/>
    <rect x="80" y="80" width="35" height="4" rx="2"/>
    <!-- Checkmark -->
    <path d="M140 90l-8 8-4-4M140 110l-8 8-4-4" stroke="#1565C0" stroke-width="3" fill="none"/>
  </g>
  
  <!-- Magnifying Glass -->
  <circle cx="120" cy="130" r="15" stroke="#FFFFFF" stroke-width="4" fill="none"/>
  <line x1="130" y1="140" x2="140" y2="150" stroke="#FFFFFF" stroke-width="4"/>
</svg>
