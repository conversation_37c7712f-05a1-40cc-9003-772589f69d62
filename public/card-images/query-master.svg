<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:0.8"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="gridPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M0 20h40M20 0v40" fill="none" stroke="#FFA500" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- Card Background -->
  <rect width="100%" height="100%" rx="15" fill="url(#cardGradient)"/>
  <rect width="100%" height="100%" rx="15" fill="url(#gridPattern)"/>
  
  <!-- Database Icon -->
  <g transform="translate(150, 180)" filter="url(#glow)" fill="none" stroke="white" stroke-width="4">
    <path d="M-60 -40 L60 -40 L60 40 L-60 40 Z"/>
    <path d="M-60 -13 L60 -13"/>
    <path d="M-60 13 L60 13"/>
    <circle cx="-45" cy="-27" r="3" fill="white"/>
    <circle cx="-45" cy="0" r="3" fill="white"/>
    <circle cx="-45" cy="27" r="3" fill="white"/>
  </g>
  
  <!-- Query Lines -->
  <g transform="translate(150, 180)" stroke="white" stroke-width="2" opacity="0.6">
    <path d="M-40 -60 C-40 -20, 40 -20, 40 -60" fill="none"/>
    <path d="M-30 -50 L-50 -50" stroke-linecap="round"/>
    <path d="M30 -50 L50 -50" stroke-linecap="round"/>
  </g>
  
  <!-- Card Title -->
  <text x="150" y="300" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold" filter="url(#glow)">
    Query Master
  </text>
  
  <!-- Rarity Indicators -->
  <g transform="translate(30, 30)">
    <circle cx="0" cy="0" r="5" fill="white"/>
    <circle cx="15" cy="0" r="5" fill="white"/>
    <circle cx="30" cy="0" r="5" fill="white"/>
  </g>
  
  <!-- Card Border -->
  <rect width="100%" height="100%" rx="15" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
