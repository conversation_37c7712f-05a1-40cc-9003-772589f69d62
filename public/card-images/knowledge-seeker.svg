<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9400D3;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#8A2BE2;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#9370DB;stop-opacity:0.8"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="gridPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M0 20h40M20 0v40" fill="none" stroke="#8A2BE2" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- Card Background -->
  <rect width="100%" height="100%" rx="15" fill="url(#cardGradient)"/>
  <rect width="100%" height="100%" rx="15" fill="url(#gridPattern)"/>
  
  <!-- Book Stack -->
  <g transform="translate(150, 180)" filter="url(#glow)">
    <!-- Bottom Book -->
    <path d="M-50 20 L50 20 L50 40 L-50 40 Z" fill="white" opacity="0.8"/>
    <!-- Middle Book -->
    <path d="M-45 0 L55 0 L55 20 L-45 20 Z" fill="white" opacity="0.9"/>
    <!-- Top Book -->
    <path d="M-40 -20 L60 -20 L60 0 L-40 0 Z" fill="white"/>
    <!-- Book Pages -->
    <path d="M-38 -18 L58 -18" stroke="#9400D3" stroke-width="1"/>
    <path d="M-43 2 L53 2" stroke="#9400D3" stroke-width="1"/>
    <path d="M-48 22 L48 22" stroke="#9400D3" stroke-width="1"/>
  </g>
  
  <!-- Floating Particles -->
  <g transform="translate(150, 180)" fill="white">
    <circle cx="-60" cy="-40" r="2">
      <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="60" cy="-30" r="2">
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-50" r="2">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Card Title -->
  <text x="150" y="300" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold" filter="url(#glow)">
    Knowledge Seeker
  </text>
  
  <!-- Rarity Indicators -->
  <g transform="translate(30, 30)">
    <circle cx="0" cy="0" r="5" fill="white"/>
    <circle cx="15" cy="0" r="5" fill="white"/>
  </g>
  
  <!-- Card Border -->
  <rect width="100%" height="100%" rx="15" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
