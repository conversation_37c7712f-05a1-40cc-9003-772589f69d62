<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9400D3;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#8A2BE2;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#9370DB;stop-opacity:0.8"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="gridPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M0 20h40M20 0v40" fill="none" stroke="#8A2BE2" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- Card Background -->
  <rect width="100%" height="100%" rx="15" fill="url(#cardGradient)"/>
  <rect width="100%" height="100%" rx="15" fill="url(#gridPattern)"/>
  
  <!-- Database Cylinder -->
  <g transform="translate(150, 180)" filter="url(#glow)">
    <path d="M-40 -60 C-40 -70, 40 -70, 40 -60 L40 60 C40 70, -40 70, -40 60 Z" 
          fill="none" stroke="white" stroke-width="4"/>
    <path d="M-40 -60 C-40 -50, 40 -50, 40 -60" fill="none" stroke="white" stroke-width="4"/>
    <path d="M-40 0 C-40 10, 40 10, 40 0" fill="none" stroke="white" stroke-width="4"/>
  </g>
  
  <!-- Data Flow Lines -->
  <g transform="translate(150, 180)" stroke="white" stroke-width="2" opacity="0.6">
    <path d="M-60 -30 L-20 -30 L-20 30 L-60 30" fill="none"/>
    <path d="M60 -30 L20 -30 L20 30 L60 30" fill="none"/>
  </g>
  
  <!-- Card Title -->
  <text x="150" y="300" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold" filter="url(#glow)">
    Database Expert
  </text>
  
  <!-- Rarity Indicators -->
  <g transform="translate(30, 30)">
    <circle cx="0" cy="0" r="5" fill="white"/>
    <circle cx="15" cy="0" r="5" fill="white"/>
  </g>
  
  <!-- Card Border -->
  <rect width="100%" height="100%" rx="15" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
