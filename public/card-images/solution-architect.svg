<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:0.8"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="gridPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M0 20h40M20 0v40" fill="none" stroke="#FFA500" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- Card Background -->
  <rect width="100%" height="100%" rx="15" fill="url(#cardGradient)"/>
  <rect width="100%" height="100%" rx="15" fill="url(#gridPattern)"/>
  
  <!-- Blueprint Grid -->
  <g transform="translate(150, 180)" stroke="white" stroke-width="0.5" opacity="0.3">
    <path d="M-70 -70 L70 -70 L70 70 L-70 70 Z" fill="none"/>
    <path d="M-70 -35 L70 -35"/>
    <path d="M-70 0 L70 0"/>
    <path d="M-70 35 L70 35"/>
    <path d="M-35 -70 L-35 70"/>
    <path d="M0 -70 L0 70"/>
    <path d="M35 -70 L35 70"/>
  </g>
  
  <!-- Architecture Elements -->
  <g transform="translate(150, 180)" filter="url(#glow)" fill="none" stroke="white" stroke-width="4">
    <!-- Central Structure -->
    <path d="M0 -50 L-40 0 L0 50 L40 0 Z"/>
    <!-- Connecting Lines -->
    <path d="M0 -50 L0 -70"/>
    <path d="M40 0 L60 0"/>
    <path d="M-40 0 L-60 0"/>
    <path d="M0 50 L0 70"/>
    <!-- Connection Points -->
    <circle cx="0" cy="-70" r="5" fill="white"/>
    <circle cx="-60" cy="0" r="5" fill="white"/>
    <circle cx="60" cy="0" r="5" fill="white"/>
    <circle cx="0" cy="70" r="5" fill="white"/>
  </g>
  
  <!-- Card Title -->
  <text x="150" y="300" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold" filter="url(#glow)">
    Solution Architect
  </text>
  
  <!-- Rarity Indicators -->
  <g transform="translate(30, 30)">
    <circle cx="0" cy="0" r="5" fill="white"/>
    <circle cx="15" cy="0" r="5" fill="white"/>
    <circle cx="30" cy="0" r="5" fill="white"/>
  </g>
  
  <!-- Card Border -->
  <rect width="100%" height="100%" rx="15" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg>
