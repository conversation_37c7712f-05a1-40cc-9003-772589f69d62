<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9400D3;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#8A2BE2;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#9370DB;stop-opacity:0.8"/>
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <pattern id="pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M0 20h40M20 0v40" fill="none" stroke="#8A2BE2" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- Card Background -->
  <rect width="100%" height="100%" rx="15" fill="url(#cardGradient)"/>
  <rect width="100%" height="100%" rx="15" fill="url(#pattern)"/>
  
  <!-- Category Icon -->
  <g transform="translate(150, 200)" filter="url(#glow)">
    
  </g>
  
  <!-- Card Name -->
  <text x="150" y="350" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">
    Data Architect
  </text>
  
  <!-- Rarity Indicator -->
  <g transform="translate(20, 20)">
    <circle cx="0" cy="0" r="5" fill="white"/><circle cx="15" cy="0" r="5" fill="white"/>
  </g>
</svg>