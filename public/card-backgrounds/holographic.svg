<svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="rainbow" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF0000;stop-opacity:0.2">
        <animate attributeName="stop-color" values="#FF0000;#FF7F00;#FFFF00;#00FF00;#0000FF;#4B0082;#8F00FF;#FF0000" dur="3s" repeatCount="indefinite"/>
      </stop>
      <stop offset="50%" style="stop-color:#FF7F00;stop-opacity:0.15">
        <animate attributeName="stop-color" values="#FF7F00;#FFFF00;#00FF00;#0000FF;#4B0082;#8F00FF;#FF0000;#FF7F00" dur="3s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" style="stop-color:#FFFF00;stop-opacity:0.2">
        <animate attributeName="stop-color" values="#FFFF00;#00FF00;#0000FF;#4B0082;#8F00FF;#FF0000;#FF7F00;#FFFF00" dur="3s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>
    <filter id="noise">
      <feTurbulence type="fractalNoise" baseFrequency="0.5" numOctaves="5" stitchTiles="stitch"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1, 0 0 0 0 1, 0 0 0 0 1, 0 0 0 0.1 0"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="url(#rainbow)"/>
  <rect width="100%" height="100%" filter="url(#noise)"/>
</svg>
