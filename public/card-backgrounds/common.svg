<svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#808080;stop-opacity:0.2"/>
      <stop offset="50%" style="stop-color:#A9A9A9;stop-opacity:0.15"/>
      <stop offset="100%" style="stop-color:#696969;stop-opacity:0.2"/>
    </linearGradient>
    <filter id="noise">
      <feTurbulence type="fractalNoise" baseFrequency="0.5" numOctaves="5" stitchTiles="stitch"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.5, 0 0 0 0 0.5, 0 0 0 0 0.5, 0 0 0 0.1 0"/>
    </filter>
  </defs>
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>
  <rect width="100%" height="100%" filter="url(#noise)"/>
</svg>
