{"peacock.color": "#215732", "css.lint.unknownAtRules": "ignore", "workbench.colorCustomizations": {"activityBar.activeBackground": "#2f7c47", "activityBar.background": "#2f7c47", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#422c74", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#2f7c47", "statusBar.background": "#215732", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#2f7c47", "statusBarItem.remoteBackground": "#215732", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#215732", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#21573299", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.remoteColor": "#215732", "workbench.colorTheme": "Default Dark Modern", "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "explorer.confirmDelete": false, "explorer.compactFolders": false, "scm.compactFolders": false, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.format.enable": true, "editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "git.enableSmartCommit": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "workbench.auxiliaryActivityBar.location": "hidden", "workbench.iconTheme": "gruvbox-material-icons", "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always"}