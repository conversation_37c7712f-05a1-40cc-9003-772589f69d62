server {
    server_name codehippo.fun www.codehippo.fun;

    root /var/www/codehippo.fun/html/;
    index index.html index.htm index.nginx-debian.html;
    
    location / {
        proxy_pass http://127.0.0.1:2358;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme; 
    }
}

server {
    if ($host = www.codehippo.fun) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    if ($host = codehippo.fun) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen       80;
    listen       [::]:80;
    
    server_name codehippo.fun www.codehippo.fun;

    return 404; # managed by Certbot

}
