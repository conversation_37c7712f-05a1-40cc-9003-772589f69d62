x-logging: &default-logging
  logging:
    driver: json-file
    options:
      max-size: 100M

services:
  nginx:
    container_name: nginx-test
    image: nginx
    volumes:
      - ./default.conf:/etc/nginx/nginx.conf
    ports:
      - '80:80'
    depends_on:
      server:
        condition: service_started
        restart: true
    restart: always
    privileged: true

  server:
    container_name: server-test
    image: judge0/judge0:1.13.1
    volumes:
      - ./judge0.conf:/judge0.conf:ro
    ports:
      - '2358:2358'
    privileged: true
    <<: *default-logging
    restart: always

  workers:
    image: judge0/judge0:1.13.1
    command: ['./scripts/workers']
    volumes:
      - ./judge0.conf:/judge0.conf:ro
    privileged: true
    <<: *default-logging
    restart: always

  db:
    image: postgres:16.2
    env_file: judge0.conf
    volumes:
      - data:/var/lib/postgresql/data/
    <<: *default-logging
    restart: always

  redis:
    image: redis:7.2.4
    command:
      [
        'bash',
        '-c',
        'docker-entrypoint.sh --appendonly no --requirepass "$$REDIS_PASSWORD"',
      ]
    env_file: judge0.conf
    <<: *default-logging
    restart: always

volumes:
  data:
