'use client';

import { <PERSON>, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';

import { Button } from '@/shadcn/button';

const ThemeSwitcher = () => {
  const { theme, setTheme } = useTheme();

  const handleToggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-9 w-9 font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors"
      onClick={handleToggleTheme}
      name="toggle theme"
    >
      <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />

      <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />

      <span className="sr-only">Toggle theme</span>
    </Button>
  );
};

export { ThemeSwitcher };
