'use client';

import { HTMLAttributes } from 'react';
import { python } from '@codemirror/lang-python';
import { EditorState } from '@codemirror/state';
import { githubLight } from '@uiw/codemirror-theme-github';
import { nord } from '@uiw/codemirror-theme-nord';
import CodeMirror from '@uiw/react-codemirror';
import { basicSetup, EditorView } from 'codemirror';
import { useTheme } from 'next-themes';
import CodeMirrorMerge from 'react-codemirror-merge';

const Original = CodeMirrorMerge.Original;
const Modified = CodeMirrorMerge.Modified;

interface Props extends HTMLAttributes<HTMLDivElement> {
  defaultValue: string;
  value: string;
  showSolution?: boolean;
  solutionCode?: string;
  solutionContent?: string;
  onCodeChange: (code: string) => void;
}

const Editor = ({
  defaultValue,
  value,
  showSolution = false,
  solutionCode,
  solutionContent,
  onCodeChange,
}: Props) => {
  const { theme } = useTheme();

  const themes = {
    light: githubLight,
    dark: nord,
  };

  const currentTheme = themes[theme as 'dark' | 'light'];

  return showSolution ? (
    <div className={'size-full px-4 py-2'}>
      <CodeMirrorMerge
        theme={currentTheme}
        className="h-full"
      >
        <Original
          value={value}
          extensions={[python()]}
        />

        <Modified
          value={solutionCode}
          extensions={[
            EditorView.editable.of(false),
            EditorState.readOnly.of(true),
            basicSetup,
            python(),
          ]}
        />
      </CodeMirrorMerge>
    </div>
  ) : (
    <div className={'size-full px-4 py-2'}>
      <CodeMirror
        defaultValue={defaultValue}
        value={value}
        height="100%"
        width="100%"
        extensions={[basicSetup, python()]}
        onChange={onCodeChange}
        theme={currentTheme}
      />
    </div>
  );
};

export { Editor };
