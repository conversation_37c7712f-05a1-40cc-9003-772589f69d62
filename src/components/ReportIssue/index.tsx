'use client';

import { ChangeEvent, useState } from 'react';
import { useParams } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Textarea,
} from '@/shadcn';
import { useUser } from '@clerk/nextjs';
import { useMutation, useQuery } from 'convex/react';
import { MessageCircleWarning } from 'lucide-react';
import { toast } from 'sonner';

import { LessonParamsProps } from '@/types/general';

const ReportIssue = () => {
  const [reportContent, setReportContent] = useState<string>('');
  const mutationSaveReport = useMutation(api.reports.saveReport);
  const { user } = useUser();
  const params = useParams<LessonParamsProps>();
  const { lesson, course } = params;

  const content = useQuery(api.lessons.getLessonContent, {
    lesson,
    course,
  });

  const sendReport = async () => {
    if (!content?._id || !user?.id) {
      return;
    }

    const body = {
      content: reportContent,
      email: user?.primaryEmailAddress?.emailAddress ?? '',
      user_id: user?.id ?? '',
      lesson_id: content?._id,
    };

    const response = await mutationSaveReport({
      ...body,
    });

    toast('Report sent');
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="default"
          size={'sm'}
          className="flex items-center text-sm"
        >
          <MessageCircleWarning className="mr-2" />

          <span>Report an issue</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Report an issue</DialogTitle>
          <DialogDescription>Describe your problem</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Textarea
              className="col-span-4"
              placeholder="Type your message here."
              onChange={(e: ChangeEvent<HTMLTextAreaElement>) => {
                setReportContent(e.target.value);
              }}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            onClick={sendReport}
            type="submit"
          >
            Send report
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export { ReportIssue };
