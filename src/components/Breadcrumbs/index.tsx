'use client';

import { Fragment } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { NextJsUrlType } from '@/types/general';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/shadcn/breadcrumb';

const generateBreadcrumbs = (pathname: string) => {
  const paths = pathname.split('/').filter(Boolean);
  return paths.map((path, index) => {
    const href = `/${paths.slice(0, index + 1).join('/')}`;
    const label =
      path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' ');
    const isLast = index === paths.length - 1;

    return { href, label, isLast };
  });
};

export function Breadcrumbs() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  if (pathname.startsWith('/admin')) return null;
  if (breadcrumbs.length === 0) return null;

  return (
    <Breadcrumb className="mb-3 bg-background border border-border rounded-md px-3 py-1.5 shadow-sm">
      <BreadcrumbList className="text-xs sm:text-sm font-mono">
        <BreadcrumbItem>
          <BreadcrumbLink
            asChild
            className="text-muted-foreground hover:text-primary transition-colors"
          >
            <Link href="/">Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator className="text-muted-foreground/50" />

        {breadcrumbs.map(({ href, label, isLast }, index) =>
          isLast ? (
            <BreadcrumbItem key={index}>
              <BreadcrumbPage className="text-foreground font-mono">
                {label}
              </BreadcrumbPage>
            </BreadcrumbItem>
          ) : (
            <Fragment key={index}>
              <BreadcrumbItem>
                <BreadcrumbLink
                  asChild
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Link href={href as NextJsUrlType}>{label}</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>

              <BreadcrumbSeparator className="text-muted-foreground/50" />
            </Fragment>
          )
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
