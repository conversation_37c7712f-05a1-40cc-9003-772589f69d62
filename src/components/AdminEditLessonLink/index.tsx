'use client';

import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Button } from '@/components';

import { EditLessonParamsProps } from '@/types/general';

const AdminEditLessonLink = () => {
  const params = useParams<EditLessonParamsProps>();

  const { course, lesson } = params;

  return (
    <>
      <Link href={`/admin/edit/lesson/${course}/${lesson}`}>
        <Button variant={'link'}>Edit this lesson</Button>
      </Link>
    </>
  );
};

export default AdminEditLessonLink;
