'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Editor,
  EditorControlButtons,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  ScrollArea,
  ScrollBar,
} from '@/components';
import { useIsClient } from '@uidotdev/usehooks';
import axios from 'axios';

const DEMO_CODE = `# Welcome to Python!
# This is a simple example to get you started.

def greet(name):
    """Return a greeting message for the given name."""
    return f"Hello, {name}! Welcome to interactive coding."

# Try calling the function
message = greet("Learner")
print(message)

# Now try changing the name and see what happens!
`;

const HomePageDemo = () => {
  const [code, setCode] = useState(DEMO_CODE);
  const [consoleOutput, setConsoleOutput] = useState('');
  const [codeIsRunning, setCodeIsRunning] = useState(false);
  const [showSolution, setShowSolution] = useState(false);

  const isClient = useIsClient();

  if (!isClient) {
    return <></>;
  }

  const onCodeChange = (code: string) => setCode(code);

  const onRun = () => {
    setCodeIsRunning(true);
    setConsoleOutput('');

    // Using the same execution endpoint as the main app
    const baseUrl = 'http://209.38.178.92:2358';

    axios
      .post(baseUrl + '/submissions?base64_encoded=false&wait=true', {
        source_code: code,
        language_id: 71,
      })
      .then(response => {
        setConsoleOutput(response.data?.stdout || 'No output');
      })
      .catch(error => {
        console.error(error);
        setConsoleOutput('Error: ' + error.message);
      })
      .finally(() => {
        setCodeIsRunning(false);
      });
  };

  const onResetCode = () => {
    setCode(DEMO_CODE);
    setConsoleOutput('');
  };

  const onSolutionReveal = () => {
    // For demo, we don't need an actual solution
    setShowSolution(current => !current);
  };

  // Dummy function for the submit button in demo mode
  const onSubmit = () => {
    setConsoleOutput(
      'Great job! Sign in to save your progress and access more lessons.'
    );
  };

  return (
    <div className="border rounded-lg overflow-hidden shadow-lg bg-white dark:bg-gruvbox-bg-dark">
      <div className="p-4 border-b bg-gruvbox-green-dark/10 dark:bg-gruvbox-green-dark/20">
        <h2 className="text-xl font-semibold text-gruvbox-bg-dark dark:text-white">
          Interactive Python Demo
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
          Try out our interactive code editor with this simple Python example
        </p>
      </div>

      <ResizablePanelGroup
        direction="horizontal"
        className="min-h-[500px]"
      >
        <ResizablePanel
          defaultSize={50}
          minSize={30}
        >
          <ScrollArea className="h-full w-full p-4">
            <div className="prose dark:prose-invert">
              <h3 className="p-2 text-xl font-bold">
                Getting Started with Python
              </h3>
              <p className="p-2 text-l">
                Welcome to our interactive Python learning environment! This
                demo shows how our platform works. Here's what you can do:
              </p>

              <ul className="p-2 list-disc ml-4 inline-block">
                <li>
                  <strong>Edit the code</strong> in the editor on the right
                </li>
                <li>
                  <strong>Run your code</strong> to see the output in the
                  console below
                </li>
                <li>
                  <strong>Reset</strong> to start over if you make a mistake
                </li>
                <li>
                  <strong>Submit</strong> when you're satisfied with your
                  solution
                </li>
              </ul>

              <h4 className="p-2 text-l font-bold">Try This:</h4>

              <ol className="list-decimal p-2 pl-6 ml-4 inline-block">
                <li>
                  Change the name in the <code>greet("Learner")</code> function
                  call
                </li>
                <li>Add another line to print something else</li>
                <li>Create a new function that does a simple calculation</li>
              </ol>

              <div className="bg-yellow-50 dark:bg-yellow-900/30 p-3 rounded-md mt-4 border border-yellow-200 dark:border-yellow-700">
                <p className="text-sm">
                  <strong>Sign in</strong> to access our full curriculum with
                  dozens of interactive lessons, track your progress, and earn
                  achievements!
                </p>
              </div>
            </div>
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        </ResizablePanel>

        <ResizableHandle withHandle />

        <ResizablePanel
          defaultSize={50}
          minSize={30}
        >
          <ResizablePanelGroup direction="vertical">
            <ResizablePanel
              defaultSize={60}
              minSize={30}
            >
              <div className="flex size-full">
                <Editor
                  onCodeChange={onCodeChange}
                  defaultValue={DEMO_CODE}
                  value={code}
                  showSolution={false}
                  solutionCode=""
                  solutionContent=""
                />
              </div>
            </ResizablePanel>

            <ResizableHandle withHandle />

            <ResizablePanel
              defaultSize={40}
              minSize={20}
            >
              <div className="flex size-full flex-col pb-6 @container">
                <EditorControlButtons
                  onRun={onRun}
                  onSubmit={onSubmit}
                  onResetCode={onResetCode}
                  onSolutionReveal={onSolutionReveal}
                  showSolution={showSolution}
                  codeIsRunning={codeIsRunning}
                />

                <Console output={consoleOutput} />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export { HomePageDemo };
