'use client';

import React from 'react';
import { Route } from 'next';
import Link from 'next/link';
import {
  BarChart,
  BookHeart,
  BookOpen, // for Add Lesson
  FileBarChart, // for Admin
  LayoutDashboard, // for Python Roadmap
  PlusCircle, // for Admin Dashboard
  Settings, // for Collectible Cards
  Shield, // for Courses
  Trophy,
} from 'lucide-react';

import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/shadcn/menubar';
import { Notifications } from '@/components/Notifications';

import ProtectedAdmin from '../ProtectedAdmin';
import { ThemeSwitcher } from '../ThemeSwitcher';

const MenuBar = () => {
  const adminRoutes: { title: string; href: Route; icon: React.ReactNode }[] = [
    {
      title: 'Admin dashboard',
      href: '/admin',
      icon: <LayoutDashboard className="h-4 w-4" />,
    },
    {
      title: 'Courses settings',
      href: '/admin/settings',
      icon: <Settings className="h-4 w-4" />,
    },
    {
      title: 'Add lesson',
      href: '/admin/add/lesson' as Route,
      icon: <PlusCircle className="h-4 w-4" />,
    },
    {
      title: 'Reports',
      href: '/admin/reports',
      icon: <FileBarChart className="h-4 w-4" />,
    },
  ];

  return (
    <Menubar className="border-none bg-gruvbox-bg-darker rounded-md">
      <MenubarMenu>
        <MenubarTrigger className="cursor-pointer font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors data-[state=open]:bg-gruvbox-green-dark/10 data-[state=open]:text-gruvbox-green-dark px-2 sm:px-3">
          <Link
            className="inline-flex items-center gap-1 sm:gap-2 w-full"
            href="/courses"
          >
            <BookOpen className="h-4 w-4" />
            <span className="hidden sm:inline">Courses</span>
          </Link>
        </MenubarTrigger>
      </MenubarMenu>

      <MenubarMenu>
        <MenubarTrigger className="cursor-pointer font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors data-[state=open]:bg-gruvbox-green-dark/10 data-[state=open]:text-gruvbox-green-dark px-2 sm:px-3">
          <div className="flex items-center gap-1 sm:gap-2">
            <BarChart className="h-4 w-4" />
            <span className="hidden sm:inline">Progress</span>
          </div>
        </MenubarTrigger>
        <MenubarContent className="bg-gruvbox-bg-darker backdrop-blur-sm rounded-md shadow-lg border border-gruvbox-fg-dark min-w-[180px]">
          <MenubarItem className="font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors focus:bg-gruvbox-green-dark/10 focus:text-gruvbox-green-dark">
            <Link
              className="inline-flex items-center gap-2 w-full py-1.5"
              href="/progress"
            >
              <BarChart className="h-4 w-4" />
              Course Progress
            </Link>
          </MenubarItem>
          <MenubarSeparator className="bg-gruvbox-fg-dark" />

          <MenubarItem className="font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors focus:bg-gruvbox-green-dark/10 focus:text-gruvbox-green-dark">
            <Link
              className="inline-flex items-center gap-2 w-full py-1.5"
              href="/achievements"
            >
              <Trophy className="h-4 w-4" />
              Achievements
            </Link>
          </MenubarItem>
          <MenubarSeparator className="bg-gruvbox-fg-dark" />

          <MenubarItem className="font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors focus:bg-gruvbox-green-dark/10 focus:text-gruvbox-green-dark">
            <Link
              className="inline-flex items-center gap-2 w-full py-1.5"
              href="/collectibles"
            >
              <BookHeart className="h-4 w-4" />
              Collectible Cards
            </Link>
          </MenubarItem>
        </MenubarContent>
      </MenubarMenu>

      <ProtectedAdmin>
        <MenubarMenu>
          <MenubarTrigger className="cursor-pointer font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors data-[state=open]:bg-gruvbox-green-dark/10 data-[state=open]:text-gruvbox-green-dark px-2 sm:px-3">
            <div className="flex items-center gap-1 sm:gap-2">
              <Shield className="h-4 w-4" />
              <span className="hidden sm:inline">Admin</span>
            </div>
          </MenubarTrigger>
          <MenubarContent className="bg-gruvbox-bg-darker backdrop-blur-sm rounded-md shadow-lg border border-gruvbox-fg-dark min-w-[180px]">
            {adminRoutes.map((route, index) => (
              <React.Fragment key={route.href}>
                <MenubarItem className="font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors focus:bg-gruvbox-green-dark/10 focus:text-gruvbox-green-dark">
                  <Link
                    className="inline-flex items-center gap-2 w-full py-1.5"
                    href={route.href}
                  >
                    {route.icon}
                    {route.title}
                  </Link>
                </MenubarItem>
                {index < adminRoutes.length - 1 && (
                  <MenubarSeparator className="bg-gruvbox-fg-dark" />
                )}
              </React.Fragment>
            ))}
          </MenubarContent>
        </MenubarMenu>
      </ProtectedAdmin>

      <MenubarMenu>
        <MenubarTrigger className="cursor-pointer font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors data-[state=open]:bg-gruvbox-green-dark/10 data-[state=open]:text-gruvbox-green-dark px-2 sm:px-3">
          <Link
            className="inline-flex items-center gap-1 sm:gap-2 w-full"
            href="/settings"
          >
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Settings</span>
          </Link>
        </MenubarTrigger>
      </MenubarMenu>

      <Notifications />

      <ThemeSwitcher />
    </Menubar>
  );
};

export { MenuBar };
