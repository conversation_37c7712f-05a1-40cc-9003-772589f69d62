import { Id } from '@/convex/_generated/dataModel';
import z from 'zod';

const LessonEditorSchema = z.object({
  _id: z.custom<Id<'lessons'>>().optional(),
  language: z.string(),
  chapter_id: z.custom<Id<'chapters'>>(),
  course_id: z.custom<Id<'courses'>>(),
  is_published: z.boolean(),
  slug: z.string().min(1),
  name: z.string().min(1),
  lesson_content: z.string().min(1),
  initial_code: z.string().min(1),
  solution_content: z.string().min(1),
  solution_code: z.string().min(1),
});

export type LessonEditorSchemaType = z.infer<typeof LessonEditorSchema>;
export { LessonEditorSchema };
