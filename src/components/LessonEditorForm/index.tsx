'use client';

import { useCallback, useEffect } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Switch,
  Textarea,
} from '@/components';
import { api } from '@/convex/_generated/api';
import { Doc, Id } from '@/convex/_generated/dataModel';
import { python } from '@codemirror/lang-python';
import { zodResolver } from '@hookform/resolvers/zod';
import { nord } from '@uiw/codemirror-theme-nord';
import ReactCodeMirror from '@uiw/react-codemirror';
import MarkdownPreview from '@uiw/react-markdown-preview';
import { useMutation, useQuery } from 'convex/react';
import { isEmpty } from 'radash';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { AdminLessonMode, EditLessonParamsProps } from '@/types/general';

import { LessonEditorSchema, LessonEditorSchemaType } from './schema';

type Props = {
  mode: AdminLessonMode;
};

// @TODO rewrite to server side component
const LessonEditorForm = ({ mode }: Props) => {
  const params = useParams<EditLessonParamsProps>();
  const router = useRouter();

  const { course = '', lesson = '' } = params;

  const lessonContent = useQuery(api.lessons.getLessonContent, {
    course,
    lesson,
  });

  const patchLessonMutation = useMutation(api.lessons.patchLesson);
  const createLessonMutation = useMutation(api.lessons.createLesson);

  const defaultValues: Omit<Doc<'lessons'>, 'order' | '_creationTime'> = {
    _id: '' as Id<'lessons'>,
    name: '',
    language: '',
    is_published: false,
    chapter_id: undefined as unknown as Id<'chapters'>,
    course_id: undefined as unknown as Id<'courses'>,
    slug: lesson ?? '',
    lesson_content: '',
    initial_code: '',
    solution_content: '',
    solution_code: '',
  };

  const form = useForm<LessonEditorSchemaType>({
    resolver: zodResolver(LessonEditorSchema),
    defaultValues,
  });

  const {
    control,
    handleSubmit: formHandleSubmit,
    formState: { errors },
    reset,
    getValues,
  } = form;

  if (!isEmpty(errors)) {
    console.error({ errors });
  }

  const fetchLessonInfo = useCallback(async () => {
    if (mode === 'edit' && lessonContent) {
      reset(lessonContent);
    }
  }, [mode, lesson, reset]);

  useEffect(() => {
    fetchLessonInfo();
  }, [fetchLessonInfo]);

  const onSubmit = async () => {
    const values = getValues();
    const result = LessonEditorSchema.safeParse(values);

    if (!result.success) {
      toast("Form can't be parsed correctly");
    } else {
      try {
        if (mode === 'edit') {
          // Make sure _id is included in the patch mutation
          if (!values._id) {
            toast.error('Lesson ID is missing');
            return;
          }
          await patchLessonMutation({
            ...values,
            _id: values._id as any,
          });
        } else {
          await createLessonMutation({
            ...values,
            name: values?.name ?? '',
            _id: values._id as any,
          });
        }

        toast.success('Lesson saved successfully');
      } catch (error) {
        console.error('Error saving lesson:', error);
        toast.error('Failed to save lesson');
      }
    }
  };

  const generatePreview = async () => {
    const values = getValues();
    const result = LessonEditorSchema.safeParse(values);

    if (!result.success) {
      toast("Form can't be parsed correctly");
    } else {
      try {
        await patchLessonMutation({
          ...values,
          _id: process.env.NEXT_PUBLIC_PREVIEW_ID! as Id<'lessons'>,
          slug: process.env.NEXT_PUBLIC_PREVIEW_SLUG!,
          is_published: false,
        });

        toast('Preview saved');

        router.push(`/${values.slug}/preview`);
      } catch (error) {
        console.error('Error saving preview:', error);
      }
    }
  };

  const courses = useQuery(api.courses.listCourses);
  const chapters = useQuery(
    api.chapters.getChapters,
    form.watch('course_id')
      ? { course_id: form.watch('course_id') as Id<'courses'> }
      : 'skip'
  );

  return (
    <Form {...form}>
      <form
        onSubmit={formHandleSubmit(onSubmit)}
        className="container mx-auto max-w-7xl px-4 py-8 space-y-8"
      >
        <p className="text-2xl font-bold">
          {mode === 'edit'
            ? 'EDIT' + ' course: ' + params.course + ' lesson: ' + params.lesson
            : 'ADD'}
        </p>

        <div className="flex flex-col">
          {mode === 'edit' && (
            <div>
              <Link href="/admin/add/lesson">
                <Button variant="link">Add new lesson</Button>
              </Link>
            </div>
          )}
          <div>
            <Button
              type="button"
              onClick={generatePreview}
              variant="link"
            >
              Preview this lesson
            </Button>
          </div>
        </div>

        <Separator className="my-4 py-1" />

        <section className="flex flex-col md:flex-row justify-between gap-6 bg-card p-6 rounded-lg border shadow-sm">
          {/* Language */}
          <div className="mr-2">
            <FormField
              control={control}
              name="language"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">Language</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                    required
                    disabled={mode === 'edit'}
                  >
                    <FormControl>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="python">Python</SelectItem>
                      <SelectItem
                        disabled
                        value="node"
                      >
                        Node
                      </SelectItem>
                      <SelectItem
                        disabled
                        value="csharp"
                      >
                        c# (csharp)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Name */}
          <div className="mr-2">
            <FormField
              control={control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">Name</FormLabel>
                  <FormControl>
                    <Input
                      required
                      placeholder="Name"
                      {...field}
                      className="block"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* lesson slug */}
          <div className="mr-2 flex flex-col">
            <FormField
              control={control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">Slug</FormLabel>
                  <FormControl>
                    <Input
                      required
                      placeholder="Slug"
                      {...field}
                      className="block"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* is published flag */}
          <div className="mr-2 flex flex-col">
            <FormField
              control={control}
              name="is_published"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 mt-[6px] block">
                    Is published?
                  </FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="block"
                      id="isPublished"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Course selector - only show in add mode */}
          {mode === 'add' && (
            <div className="mr-2">
              <FormField
                control={control}
                name="course_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-2 inline-block">Course</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value as string}
                      required
                    >
                      <FormControl>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select course" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {courses?.map(course => (
                          <SelectItem
                            key={course._id}
                            value={course._id}
                          >
                            {course.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}

          {/* Chapter selector - only show in add mode */}
          {mode === 'add' && (
            <div className="mr-2">
              <FormField
                control={control}
                name="chapter_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-2 inline-block">Chapter</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value as string}
                      required
                      disabled={!form.watch('course_id')}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select chapter" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {chapters?.map(chapter => (
                          <SelectItem
                            key={chapter._id}
                            value={chapter._id}
                          >
                            {chapter.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </section>

        <Separator className="my-4 py-1" />

        {/* New two-column layout for content and preview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <section>
            <FormField
              control={form.control}
              name="lesson_content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">
                    Lesson content
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      className="mb-2 min-h-[400px]"
                      required
                      placeholder={'Lesson Content'}
                      {...field}
                      onChange={field.onChange}
                      value={field.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <section>
            <p className="mb-2">Preview</p>
            <MarkdownPreview
              className="min-h-[400px] border rounded-md p-4"
              source={getValues('lesson_content')}
            />
          </section>
        </div>

        <Separator className="my-4 py-1" />

        {/* New two-column layout for code editors */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <section>
            <FormField
              control={form.control}
              name="initial_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">
                    Initial code
                  </FormLabel>
                  <FormControl>
                    <ReactCodeMirror
                      value={getValues('initial_code')}
                      height="400px"
                      width="100%"
                      extensions={[python()]}
                      onChange={field.onChange}
                      theme={nord}
                      lang={getValues('language')}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <section>
            <FormField
              control={form.control}
              name="solution_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="mb-2 inline-block">
                    Solution code
                  </FormLabel>
                  <FormControl>
                    <ReactCodeMirror
                      value={field.value}
                      height="400px"
                      width="100%"
                      extensions={[python()]}
                      onChange={field.onChange}
                      theme={nord}
                      lang={getValues('language')}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>
        </div>

        {/* Solution content section */}
        <section>
          <FormField
            control={form.control}
            name="solution_content"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="mb-2 inline-block">
                  Solution content
                </FormLabel>
                <FormControl>
                  <Textarea
                    required
                    placeholder={'Solution content'}
                    {...field}
                    onChange={field.onChange}
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </section>

        {/* Submit buttons */}
        <div className="flex justify-end py-2">
          <Button
            onClick={() =>
              reset(
                mode === 'edit' ? { ...lessonContent } : { ...defaultValues }
              )
            }
            variant={'destructive'}
            type={'reset'}
          >
            Reset all
          </Button>

          <Button
            className="ml-2"
            type={'submit'}
          >
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
};

export { LessonEditorForm };
