import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { auth } from '@clerk/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import { <PERSON><PERSON><PERSON>, BookO<PERSON>, Trophy } from 'lucide-react';

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/shadcn/card';
import { Progress } from '@/shadcn/progress';

export default async function Home() {
  const a = await auth();
  const token = (await a?.getToken({ template: 'convex' })) ?? '';
  const userId = a?.userId ?? '';
  const courses = await fetchQuery(api.courses.getCourses, {}, { token });

  // Get all chapters and lessons for each course
  const coursesWithContent = await Promise.all(
    courses.map(async course => {
      const chapters = await fetchQuery(
        api.chapters.getChapters,
        { course_id: course._id },
        { token }
      );

      const lessons = await fetchQuery(
        api.lessons.getCourseLessons,
        { course_id: course._id },
        { token }
      );

      // Only include published lessons for non-admin users
      const publishedLessons = lessons.filter(lesson => lesson.is_published);

      return {
        ...course,
        hasContent: chapters.length > 0 && publishedLessons.length > 0,
      };
    })
  );

  // Filter out courses with no chapters or lessons
  const nonEmptyCourses = coursesWithContent.filter(
    course => course.hasContent
  );

  const courseProgress = !userId
    ? []
    : await Promise.all(
        nonEmptyCourses.map(async course => {
          const progress = await fetchQuery(
            api.user_progress.getUserProgress,
            { user_id: userId, course_id: course._id },
            { token }
          );

          const lessons = await fetchQuery(
            api.lessons.getCourseLessons,
            { course_id: course._id },
            { token }
          );

          const publishedLessons = lessons.filter(
            lesson => lesson.is_published
          );

          const completedLessons = progress?.filter(
            p => p.is_completed
          )?.length;

          return {
            course,
            completedLessons,
            totalLessons: publishedLessons.length,
            progressPercentage:
              publishedLessons.length > 0
                ? Math.round((completedLessons / publishedLessons.length) * 100)
                : 0,
          };
        })
      );

  return (
    <div className="container mx-auto p-4">
      <div className="mb-12 flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-mono font-bold mb-2 text-white">
            Available Courses
          </h2>
          <p className="font-mono text-gruvbox-bg-gray text-base">
            Choose from our selection of programming courses
          </p>
        </div>
        <Link
          href="/progress"
          className="flex items-center gap-2 px-4 py-2 rounded-md bg-gruvbox-green-dark/10 hover:bg-gruvbox-green-dark/20 text-gruvbox-green-dark font-mono transition-colors"
        >
          <BarChart className="h-5 w-5" />

          <span>View your progress</span>
        </Link>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {courseProgress.map(
          ({ course, completedLessons, totalLessons, progressPercentage }) => (
            <Link
              key={course._id}
              href={`/${course.slug}`}
              className="block"
            >
              <Card className="h-full transition-all duration-200 hover:shadow-lg bg-gruvbox-bg-darker border-gruvbox-bg-darker hover:border-gruvbox-green-dark/50">
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2 mb-1">
                    <BookOpen className="h-5 w-5 text-gruvbox-green-dark" />

                    <CardTitle className="text-xl font-mono font-bold text-white">
                      {course.name}
                    </CardTitle>
                  </div>
                  <p className="text-base font-mono text-gruvbox-bg-gray">
                    {completedLessons} of {totalLessons} lessons completed
                  </p>
                </CardHeader>

                <CardContent>
                  <div className="relative pt-1">
                    <Progress
                      value={progressPercentage}
                      className="h-2 bg-gruvbox-green-dark/10"
                    />

                    <div className="flex items-center justify-between mt-2">
                      <p className="text-sm font-mono font-medium text-gruvbox-green-dark">
                        {progressPercentage}% complete
                      </p>
                      {progressPercentage === 100 && (
                        <Trophy className="h-5 w-5 text-gruvbox-green-dark" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          )
        )}
      </div>
    </div>
  );
}
