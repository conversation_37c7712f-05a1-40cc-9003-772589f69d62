import { useState } from 'react';
import { cn } from '@/utils';
import { motion } from 'framer-motion';

import { CollectibleCardWithStatus } from '@/types/cards';

export function CollectibleCard({
  card,
  isCollected,
}: CollectibleCardWithStatus) {
  const [isFlipped, setIsFlipped] = useState(false);

  const rarityColors = {
    legendary: 'from-amber-400 to-amber-600',
    epic: 'from-purple-400 to-purple-600',
    rare: 'from-blue-400 to-blue-600',
    common: 'from-gray-400 to-gray-600',
  };

  const variationEffects = {
    holographic:
      'animate-gradient-xy bg-gradient-to-br from-purple-400 via-pink-500 to-blue-500',
    golden: 'bg-gradient-to-br from-yellow-200 via-amber-400 to-yellow-600',
    dark: 'bg-gradient-to-br from-gray-800 via-gray-900 to-black',
    rainbow:
      'animate-gradient-xy bg-gradient-to-br from-red-500 via-purple-500 via-blue-500 via-green-500 to-yellow-500',
    standard: 'bg-gradient-to-br from-gray-200 to-gray-400',
    frost: 'bg-gradient-to-br from-blue-100 via-blue-200 to-white',
    metallic: 'bg-gradient-to-br from-gray-300 via-gray-400 to-gray-500',
  };

  const baseCardStyle = cn(
    'relative w-full aspect-[3/4] rounded-xl transition-all duration-500 transform-gpu cursor-pointer',
    'hover:shadow-xl group',
    'before:absolute before:inset-0 before:z-10 before:rounded-xl before:shadow-[inset_0_0_2px_rgba(255,255,255,0.3)] before:pointer-events-none',
    'after:absolute after:inset-0 after:z-0 after:rounded-xl after:bg-cover after:bg-center after:opacity-20 after:mix-blend-overlay after:pointer-events-none',
    'hover:translate-y-[-8px] hover:rotate-y-[-8deg] hover:rotate-x-[8deg]',
    'hover:shadow-[0_20px_30px_-10px_rgba(0,0,0,0.3)]',
    isCollected ? 'brightness-100' : 'brightness-50 grayscale',
    !isCollected && 'hover:brightness-75 hover:grayscale-[0.7]'
  );



  const cardSideStyle =
    'absolute inset-0 w-full h-full rounded-xl backface-hidden shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]';

  const frontCardStyle = cn(
    cardSideStyle,
    'bg-gradient-to-br p-4 backdrop-blur-sm',
    rarityColors[card.rarity],
    card.variation && variationEffects[card.variation]
  );

  const backCardStyle = cn(
    cardSideStyle,
    'bg-gruvbox-bg-darker p-4 backdrop-blur-sm',
    'transform rotate-y-180'
  );

  const getRarityBadgeColor = (rarity: string) => {
    const colors = {
      legendary: 'bg-amber-500/20',
      epic: 'bg-purple-500/20',
      rare: 'bg-blue-500/20',
      common: 'bg-gray-500/20',
    };
    return colors[rarity as keyof typeof colors] || colors.common;
  };

  return (
    <div
      className={baseCardStyle}
      onClick={() => isCollected && setIsFlipped(!isFlipped)}
      style={{
        perspective: '2000px',
        transformStyle: 'preserve-3d',
      }}
    >
      <motion.div
        className="relative w-full h-full"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{
          duration: 0.8,
          type: 'spring',
          stiffness: 80,
          damping: 15,
        }}
        style={{
          transformStyle: 'preserve-3d',
        }}
      >
        {/* Front of the card */}
        <div
          className={frontCardStyle}
          style={{
            transform: `translateZ(20px)`,
            boxShadow:
              '0 8px 16px rgba(0,0,0,0.3), inset 0 -4px 8px rgba(0,0,0,0.1)',
          }}
        >
          <div className="relative h-full flex flex-col">
            {/* Card content */}
            <div className="relative z-10">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-bold text-white">{card.name}</h3>
                <span
                  className={cn(
                    'px-2 py-1 text-xs font-semibold rounded text-white',
                    getRarityBadgeColor(card.rarity)
                  )}
                >
                  {card.rarity.toUpperCase()}
                  {card.variation && ` • ${card.variation}`}
                </span>
              </div>
              <p className="text-sm text-white/90 mb-4">{card.description}</p>
            </div>

            {/* Card image */}
            <div className="flex-grow relative">
              <div
                className="absolute inset-0 bg-cover bg-center rounded-lg"
                style={{
                  backgroundImage: `url(/card-images/${card.name}.svg)`,
                }}
              />
            </div>

            {/* Stats */}
            <div className="relative z-10 mt-4 grid grid-cols-3 gap-2 text-center text-white">
              <div>
                <div className="text-xs opacity-80">Power</div>
                <div className="font-bold">{card.stats.power}</div>
              </div>
              <div>
                <div className="text-xs opacity-80">Difficulty</div>
                <div className="font-bold">{card.stats.difficulty}</div>
              </div>
              <div>
                <div className="text-xs opacity-80">Popularity</div>
                <div className="font-bold">{card.stats.popularity}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Back of the card */}
        <div
          className={backCardStyle}
          style={{
            transform: `rotateY(180deg) translateZ(20px)`,
            boxShadow:
              '0 8px 16px rgba(0,0,0,0.3), inset 0 -4px 8px rgba(0,0,0,0.1)',
          }}
        >
          <div className="h-full flex flex-col">
            <h3 className="text-lg font-bold mb-2">{card.name}</h3>

            <div className="flex-grow space-y-4">
              <div>
                <h4 className="font-semibold text-sm mb-1">Key Concepts</h4>
                <ul className="text-sm space-y-1">
                  {card.details.concepts.map((concept, index) => (
                    <li
                      key={index}
                      className="text-gray-600 dark:text-gray-400"
                    >
                      • {concept}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-sm mb-1">Pro Tips</h4>
                <ul className="text-sm space-y-1">
                  {card.details.tips.map((tip, index) => (
                    <li
                      key={index}
                      className="text-gray-600 dark:text-gray-400"
                    >
                      • {tip}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-sm mb-1">Resources</h4>
                <ul className="text-sm space-y-1">
                  {card.details.resources.map((resource, index) => (
                    <li
                      key={index}
                      className="text-gray-600 dark:text-gray-400"
                    >
                      • {resource}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Locked overlay for uncollected cards */}
      {!isCollected && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black/50 rounded-full p-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
}
