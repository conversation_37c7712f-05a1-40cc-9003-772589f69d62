import { BookOpen, Star, Target, Trophy } from 'lucide-react';

interface StatsOverviewProps {
  totalCompletedLessons: number;
  totalAchievements: number;
  totalCollectibles: number;
  streakDays: number;
}

export const StatsOverview = ({
  totalCompletedLessons,
  totalAchievements,
  totalCollectibles,
  streakDays,
}: StatsOverviewProps) => {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-12">
      <div className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-mono text-white">Total Lessons</h3>
          <div className="p-2 rounded bg-gruvbox-green-dark/10">
            <BookOpen className="h-5 w-5 text-gruvbox-green-dark" />
          </div>
        </div>
        <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
          {totalCompletedLessons}
        </p>
        <p className="text-sm font-mono text-gruvbox-bg-gray">
          lessons completed
        </p>
      </div>

      <div className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-mono text-white">Achievements</h3>
          <div className="p-2 rounded bg-gruvbox-green-dark/10">
            <Trophy className="h-5 w-5 text-gruvbox-green-dark" />
          </div>
        </div>
        <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
          {totalAchievements}
        </p>
        <p className="text-sm font-mono text-gruvbox-bg-gray">unlocked</p>
      </div>

      <div className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-mono text-white">Collectibles</h3>
          <div className="p-2 rounded bg-gruvbox-green-dark/10">
            <Star className="h-5 w-5 text-gruvbox-green-dark" />
          </div>
        </div>
        <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
          {totalCollectibles}
        </p>
        <p className="text-sm font-mono text-gruvbox-bg-gray">
          cards collected
        </p>
      </div>

      <div className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-mono text-white">Learning Streak</h3>
          <div className="p-2 rounded bg-gruvbox-green-dark/10">
            <Target className="h-5 w-5 text-gruvbox-green-dark" />
          </div>
        </div>
        <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
          {streakDays}
        </p>
        <p className="text-sm font-mono text-gruvbox-bg-gray">days in a row</p>
      </div>
    </div>
  );
};
