import Link from 'next/link';
import { Star, Trophy } from 'lucide-react';

export const QuickLinks = () => {
  return (
    <div className="mt-12 flex flex-col sm:flex-row gap-4">
      <Link
        href="/achievements"
        className="flex-1"
      >
        <div className="bg-gruvbox-bg-darker border border-gruvbox-bg-darker rounded-md p-4 transition-all duration-200 hover:border-gruvbox-green-dark/50 group">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded bg-gruvbox-green-dark/10">
              <Trophy className="h-5 w-5 text-gruvbox-green-dark" />
            </div>
            <span className="font-mono text-white group-hover:text-gruvbox-green-dark">
              View All Achievements
            </span>
          </div>
        </div>
      </Link>
      <Link
        href="/collectibles"
        className="flex-1"
      >
        <div className="bg-gruvbox-bg-darker border border-gruvbox-bg-darker rounded-md p-4 transition-all duration-200 hover:border-gruvbox-green-dark/50 group">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded bg-gruvbox-green-dark/10">
              <Star className="h-5 w-5 text-gruvbox-green-dark" />
            </div>
            <span className="font-mono text-white group-hover:text-gruvbox-green-dark">
              View Collectible Cards
            </span>
          </div>
        </div>
      </Link>
    </div>
  );
};
