import Link from 'next/link';
import { Id } from '@/convex/_generated/dataModel';
import { BookOpen, Trophy } from 'lucide-react';

import { Progress } from '@/shadcn/progress';

interface CourseProgress {
  course: {
    _id: Id<'courses'>;
    name: string;
    slug: string;
  };
  completedLessons: number;
  totalLessons: number;
  progressPercentage: number;
}

interface CourseProgressSectionProps {
  courseProgress: CourseProgress[];
}

export const CourseProgressSection = ({
  courseProgress,
}: CourseProgressSectionProps) => {
  return (
    <>
      <h2 className="text-2xl font-mono font-bold mb-6 text-white">
        Course Progress
      </h2>
      <div className="grid gap-6 md:grid-cols-2">
        {courseProgress.map(
          ({ course, completedLessons, totalLessons, progressPercentage }) => (
            <Link
              key={course._id}
              href={`/${course.slug}`}
              className="block group"
            >
              <div className="bg-gruvbox-bg-darker border border-gruvbox-bg-darker rounded-md overflow-hidden p-6 transition-all duration-200 hover:border-gruvbox-green-dark/50">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 rounded bg-gruvbox-green-dark/10">
                    <BookOpen className="h-5 w-5 text-gruvbox-green-dark" />
                  </div>
                  <div>
                    <h3 className="text-xl font-mono font-bold text-white group-hover:text-gruvbox-green-dark transition-colors">
                      {course.name}
                    </h3>
                    <p className="text-sm font-mono text-gruvbox-bg-gray">
                      {completedLessons} of {totalLessons} lessons completed
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <Progress
                    value={progressPercentage}
                    className="h-2 bg-gruvbox-green-dark/10"
                  />
                  <div className="flex items-center justify-between mt-3">
                    <p className="text-sm font-mono font-medium text-gruvbox-green-dark">
                      {progressPercentage}% complete
                    </p>
                    {progressPercentage === 100 && (
                      <Trophy className="h-5 w-5 text-gruvbox-green-dark" />
                    )}
                  </div>
                </div>
              </div>
            </Link>
          )
        )}
      </div>
    </>
  );
};
