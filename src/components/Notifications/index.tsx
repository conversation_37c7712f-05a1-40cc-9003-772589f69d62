'use client';

import { api } from '@/convex/_generated/api';
import {
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/shadcn';
import { useUser } from '@clerk/nextjs';
import { useQuery } from 'convex/react';
import { Bell } from 'lucide-react';

const Notifications = () => {
  const { user } = useUser();
  const notifications = useQuery(api.notifications.getUnreadNotifications, {
    user_id: user?.id ?? '',
  });

  const unreadCount = notifications?.length ?? 0;

  return (
    <MenubarMenu>
      <MenubarTrigger className="cursor-pointer font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors data-[state=open]:bg-gruvbox-green-dark/10 data-[state=open]:text-gruvbox-green-dark relative">
        <Bell className="h-5 w-5" />

        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-gruvbox-green-dark text-[10px] font-bold flex items-center justify-center text-white">
            {unreadCount}
          </span>
        )}
      </MenubarTrigger>

      <MenubarContent className="bg-gruvbox-bg-darker backdrop-blur-sm rounded-md shadow-lg border border-gruvbox-fg-dark min-w-[300px] max-h-[400px] overflow-y-auto">
        {notifications?.length ? (
          notifications.map(notification => (
            <div key={notification._id}>
              <MenubarItem className="font-mono text-gruvbox-bg-gray hover:bg-gruvbox-green-dark/10 hover:text-gruvbox-green-dark transition-colors focus:bg-gruvbox-green-dark/10 focus:text-gruvbox-green-dark p-3">
                <div>
                  <h4 className="font-semibold text-sm">
                    {notification.title}
                  </h4>
                  <p className="text-xs mt-1">{notification.content}</p>
                  <span className="text-[10px] text-gruvbox-gray-light mt-2 block">
                    {new Date(notification.created_at).toLocaleString()}
                  </span>
                </div>
              </MenubarItem>
              <MenubarSeparator className="bg-gruvbox-fg-dark" />
            </div>
          ))
        ) : (
          <MenubarItem className="font-mono text-gruvbox-bg-gray p-3 text-center">
            No new notifications
          </MenubarItem>
        )}
      </MenubarContent>
    </MenubarMenu>
  );
};

export { Notifications };
