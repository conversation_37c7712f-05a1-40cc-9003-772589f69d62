'use client';

import {
  JS<PERSON>,
  JSXElementConstructor,
  ReactElement,
  Suspense,
  useState,
} from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Editor,
  EditorControlButtons,
  LessonContent,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  ScrollArea,
  ScrollBar,
} from '@/components';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { useUser } from '@clerk/nextjs';
import { useIsClient } from '@uidotdev/usehooks';
import axios from 'axios';
import { useMutation } from 'convex/react';
import { MDXComponents } from 'mdx/types';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shadcn/alert-dialog';

// import '@/styling/rehype-themes/obsidian.css';
// import '@/styling/rehype-themes/github-dark.css';
// import '@/styling/rehype-themes/github-light.css';
// import '@/styling/rehype-themes/monokai.css';
// import '@/styling/rehype-themes/atom-one-dark.css';
// import '@/styling/rehype-themes/arduino-light.css';
// import '@/styling/rehype-themes/gruvbox-dark.css';
import '@/styling/rehype-themes/gruvbox-light.css';
import '@/styling/rehype-themes/nord.css';

import router from 'next/router';

type Props = {
  mdxComponents: ReactElement<
    MDXComponents,
    string | JSXElementConstructor<unknown>
  >;
  defaultLayout:
    | {
        mainlayout: readonly number[] | undefined;
        editorlayout: readonly number[] | undefined;
      }
    | undefined;
  lessonContent: Doc<'lessons'>;
};

const EditorManager = ({
  lessonContent,
  mdxComponents,
  defaultLayout,
}: Props): JSX.Element => {
  const { initial_code, order, solution_code, solution_content } =
    lessonContent;
  const { user } = useUser();
  const addXP = useMutation(api.users.addExperience);

  const [code, setCode] = useState(initial_code);
  const [showSolution, setShowSolution] = useState(false);
  const [codeIsRunning, setCodeIsRunning] = useState(false);
  const [consoleOutput, setConsoleOutput] = useState('');
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const nextLessonSlug = '';

  const onMainLayoutChange = (sizes: number[]): void => {
    document.cookie = `react-resizable-panels:mainLayout=${JSON.stringify(sizes)}`;
  };

  const onEditorLayoutChange = (sizes: number[]): void => {
    document.cookie = `react-resizable-panels:editorLayout=${JSON.stringify(sizes)}`;
  };

  const onRun = () => {
    // Implement your new code execution logic here
    setCodeIsRunning(true);
    setConsoleOutput('');
    setShowSolution(false);

    // Add your new code execution implementation

    setCodeIsRunning(false);
  };

  const baseUrl = 'http://209.38.178.92:2358';

  const onSubmit = () => {
    axios
      .post(baseUrl + '/submissions?base64_encoded=false&wait=true', {
        source_code: code,
        language_id: 71,
      })
      .then(async response => {
        console.log({
          response,
        });

        setConsoleOutput(response.data?.stdout);

        // If submission is successful, award XP
        if (response.data?.stdout && user?.id) {
          // Award XP based on lesson completion
          await addXP({
            userId: user.id,
            xpAmount: 100, // Base XP for completing a lesson
          });
          setShowSuccessDialog(true);
        }
      })
      .catch(error => {
        console.error(error);
        setConsoleOutput('Error: ' + error.message);
      });
  };

  const onContinue = () => {
    router.push(nextLessonSlug);
  };

  const onResetCode = () => {
    setCode(initial_code);
    setConsoleOutput('');
  };

  const onSolutionReveal = () => {
    setConsoleOutput('');
    setShowSolution(current => !current);
  };

  const onCodeChange = (code: string) => setCode(code);

  const isClient = useIsClient();

  if (!isClient) {
    return <></>;
  }

  return (
    <>
      {/* desktop layout */}
      <div className="hidden md:block">
        <div className="flex size-full">
          <Suspense fallback={'...AsideNavbar'}>
            {/* <AsideNavbar /> */}
          </Suspense>

          <ResizablePanelGroup
            autoSaveId={'code-playground-columns'}
            direction="horizontal"
            className="size-full border"
            onLayout={onMainLayoutChange}
          >
            <ResizablePanel
              defaultSize={defaultLayout?.mainlayout?.[0]}
              minSize={25}
            >
              <ScrollArea className="h-screen w-full">
                <Suspense fallback={'....mdxComponents'}>
                  <LessonContent mdxComponents={mdxComponents} />
                </Suspense>

                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </ResizablePanel>

            <ResizableHandle withHandle />

            <ResizablePanel
              defaultSize={defaultLayout?.mainlayout?.[1]}
              minSize={30}
            >
              <ResizablePanelGroup
                direction="vertical"
                autoSaveId={'code-playground-rows'}
                onLayout={onEditorLayoutChange}
              >
                <ResizablePanel
                  defaultSize={defaultLayout?.editorlayout?.[0]}
                  minSize={30}
                >
                  <div className="flex size-full">
                    <Suspense>
                      <Editor
                        onCodeChange={onCodeChange}
                        defaultValue={initial_code}
                        value={code}
                        showSolution={showSolution}
                        solutionCode={solution_code}
                        solutionContent={solution_content}
                      />
                    </Suspense>
                  </div>
                </ResizablePanel>

                <ResizableHandle withHandle />

                <ResizablePanel
                  defaultSize={defaultLayout?.editorlayout?.[1]}
                  minSize={30}
                >
                  <div className="flex size-full flex-col pb-6 @container">
                    <EditorControlButtons
                      onRun={onRun}
                      onSubmit={onSubmit}
                      onResetCode={onResetCode}
                      onSolutionReveal={onSolutionReveal}
                      showSolution={showSolution}
                      codeIsRunning={codeIsRunning}
                    />
                    <Console output={consoleOutput} />
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>

      {/* mobile layout */}
      <div className="flex max-w-full md:hidden">
        {/* <AsideNavbar /> */}

        <ScrollArea className="h-screen w-full pb-16">
          <LessonContent mdxComponents={mdxComponents} />

          <Editor
            onCodeChange={onCodeChange}
            defaultValue={initial_code}
            value={code}
            showSolution={showSolution}
          />

          <EditorControlButtons
            onRun={onRun}
            onSubmit={onSubmit}
            onResetCode={onResetCode}
            onSolutionReveal={onSolutionReveal}
            showSolution={showSolution}
            codeIsRunning={codeIsRunning}
          />

          <Console output={consoleOutput} />

          <ScrollBar orientation="vertical" />
        </ScrollArea>
      </div>

      <AlertDialog open={showSuccessDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Congratulations! 🎉</AlertDialogTitle>
            <AlertDialogDescription>
              You've successfully completed this lesson. Ready to move on to the
              next challenge?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              asChild
              onClick={() => setShowSuccessDialog(false)}
            >
              <button className="">Stay</button>
            </AlertDialogCancel>
            <AlertDialogAction onClick={onContinue}>
              Continue to next lesson
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export { EditorManager };
