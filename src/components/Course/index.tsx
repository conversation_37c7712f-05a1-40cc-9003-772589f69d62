import Link from 'next/link';
import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { auth } from '@clerk/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import {
  BookOpen,
  CheckCircle2,
  ChevronRight,
  Lock,
  PlayCircle,
} from 'lucide-react';
import { isEmpty } from 'radash';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/accordion';

type Props = {
  course: Doc<'courses'>;
  chapters: Doc<'chapters'>[];
  lessons: Doc<'lessons'>[];
};

const Course = async ({ course, lessons }: Props) => {
  if (isEmpty(course) && !course?._id) {
    return notFound();
  }

  const a = await auth();
  const token = (await a?.getToken({ template: 'convex' })) ?? '';
  const userId = a?.userId ?? '';

  const chapters = await fetchQuery(api.chapters.getChapters, {
    course_id: course._id,
  });

  const progress = await fetchQuery(
    api.user_progress.getUserProgress,
    { user_id: userId, course_id: course._id },
    { token }
  );

  // Filter published lessons for non-admin users
  const visibleLessons = userId
    ? lessons
    : lessons.filter(lesson => lesson.is_published);

  // Filter out empty chapters
  const nonEmptyChapters = chapters.filter(chapter => {
    const chapterLessons = visibleLessons.filter(
      lesson => lesson.chapter_id === chapter._id
    );
    return chapterLessons.length > 0;
  });

  const completedLessons = progress?.filter(p => p.is_completed)?.length;
  const progressPercentage =
    visibleLessons.length > 0
      ? Math.round((completedLessons / visibleLessons.length) * 100)
      : 0;

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-4xl mx-auto">
          {/* Course Header */}
          <div className="mb-12">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2.5 rounded bg-gruvbox-green-dark/10">
                <BookOpen className="h-6 w-6 text-gruvbox-green-dark" />
              </div>
              <h1 className="text-4xl font-mono font-bold text-white">
                {course.name}
              </h1>
            </div>
            <p className="text-lg font-mono text-gruvbox-bg-gray mb-6">
              {course?.name}
              {/* {course.description} */}
            </p>
            <div className="flex items-center gap-3 font-mono text-gruvbox-green-dark">
              <div className="flex items-center gap-2">
                <PlayCircle className="h-5 w-5" />

                <span>{lessons.length} lessons</span>
              </div>
              <span className="text-gruvbox-green-dark/50">•</span>
              <span>{progressPercentage}% complete</span>
            </div>
          </div>

          {/* Course Content */}
          <div className="space-y-4">
            {nonEmptyChapters.map(chapter => {
              const chapterLessons = visibleLessons.filter(
                lesson => lesson.chapter_id === chapter._id
              );

              return (
                <Accordion
                  key={chapter._id}
                  type="single"
                  collapsible
                  className="bg-gruvbox-bg1/50 rounded-lg border border-gruvbox-bg3/20"
                >
                  <AccordionItem value={chapter._id}>
                    <AccordionTrigger className="px-6 py-4 hover:bg-gruvbox-green-dark/5 hover:no-underline data-[state=open]:bg-gruvbox-green-dark/5">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded bg-gruvbox-green-dark/10">
                          <PlayCircle className="h-5 w-5 text-gruvbox-green-dark" />
                        </div>
                        <div className="text-left">
                          <h3 className="font-mono font-bold text-white text-lg">
                            {chapter.name}
                          </h3>
                          <p className="text-sm font-mono text-gruvbox-bg-gray">
                            {chapterLessons.length} lessons
                          </p>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="border-t border-gruvbox-fg-dark">
                      <div className="divide-y divide-gruvbox-fg-dark">
                        {chapterLessons.map((lesson, index) => {
                          const isCompleted = progress?.some(
                            p => p.lesson_id === lesson._id && p.is_completed
                          );

                          return (
                            <Link
                              key={lesson._id}
                              href={`/${course.slug}/${lesson.slug}`}
                              className="flex items-center justify-between px-6 py-4 hover:bg-gruvbox-green-dark/5 transition-colors group"
                            >
                              <div className="flex items-center gap-3">
                                <div className="flex items-center gap-2">
                                  {isCompleted ? (
                                    <CheckCircle2 className="h-5 w-5 text-gruvbox-green-dark" />
                                  ) : (
                                    <Lock className="h-5 w-5 text-gruvbox-bg-gray group-hover:text-gruvbox-green-dark" />
                                  )}
                                  <span className="font-mono font-medium text-sm min-w-[24px] h-6 flex items-center justify-center rounded bg-gruvbox-green-dark/10 text-gruvbox-green-dark">
                                    {index + 1}
                                  </span>
                                </div>
                                <span className="font-mono text-white group-hover:text-gruvbox-green-dark">
                                  {lesson.name}
                                </span>
                              </div>
                              <ChevronRight className="h-5 w-5 text-gruvbox-bg-gray group-hover:text-gruvbox-green-dark" />
                            </Link>
                          );
                        })}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Course;
