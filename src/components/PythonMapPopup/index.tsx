'use client';

import { Route } from 'next';
import Link from 'next/link';
import { Book, CheckCircle, Lock, Star, Trophy, Zap, MapPin, ArrowRight } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/shadcn/dialog';

// Define the PythonMapPopup props
export type Chapter = {
  id: number;
  title: string;
  status: 'completed' | 'available' | 'locked';
  position: { x: number; y: number };
  xp: number;
  dbId?: string;
};

interface PythonMapPopupProps {
  chapters: Chapter[];
  selectedChapter: Chapter | null;
  setSelectedChapter: (chapter: Chapter | null) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Roadmap milestone data with descriptions
const roadmapMilestones = [
  {
    step: 1,
    title: "Hello World!",
    description: "Start your Python journey with basic syntax and your first program",
    skills: ["Print statements", "Basic syntax", "Running Python code"]
  },
  {
    step: 2,
    title: "Variables & Data Types",
    description: "Learn to store and manipulate different types of data",
    skills: ["Variables", "Strings", "Numbers", "Booleans"]
  },
  {
    step: 3,
    title: "Control Flow",
    description: "Master decision making and loops in your programs",
    skills: ["If statements", "For loops", "While loops", "Conditions"]
  },
  {
    step: 4,
    title: "Functions & Modules",
    description: "Organize your code with reusable functions and modules",
    skills: ["Function definition", "Parameters", "Return values", "Imports"]
  },
  {
    step: 5,
    title: "Data Structures",
    description: "Work with collections of data efficiently",
    skills: ["Lists", "Dictionaries", "Tuples", "Sets"]
  },
  {
    step: 6,
    title: "Object-Oriented Programming",
    description: "Build complex applications with classes and objects",
    skills: ["Classes", "Objects", "Inheritance", "Methods"]
  }
];

interface PythonMapPopupProps {
  chapters: Chapter[];
  selectedChapter: Chapter | null;
  setSelectedChapter: (chapter: Chapter | null) => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const PythonMapPopup = ({
  chapters,
  selectedChapter,
  setSelectedChapter,
  open,
  onOpenChange,
}: PythonMapPopupProps) => {
  // Get the appropriate icon based on chapter status
  const getStatusIcon = (status: Chapter['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-8 w-8 text-emerald-400" />;
      case 'available':
        return <MapPin className="h-8 w-8 text-blue-400" />;
      case 'locked':
        return <Lock className="h-6 w-6 text-slate-400" />;
    }
  };

  // Get milestone styling based on status
  const getMilestoneStyle = (status: Chapter['status'], isSelected: boolean) => {
    if (isSelected) {
      return {
        bg: 'bg-gradient-to-br from-purple-500 to-purple-700',
        border: 'border-purple-300 shadow-lg shadow-purple-500/50',
        text: 'text-white'
      };
    }
    
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-gradient-to-br from-emerald-500 to-emerald-700',
          border: 'border-emerald-300 shadow-lg shadow-emerald-500/50',
          text: 'text-white'
        };
      case 'available':
        return {
          bg: 'bg-gradient-to-br from-blue-500 to-blue-700',
          border: 'border-blue-300 shadow-lg shadow-blue-500/50',
          text: 'text-white'
        };
      case 'locked':
        return {
          bg: 'bg-gradient-to-br from-slate-600 to-slate-800',
          border: 'border-slate-400 shadow-lg shadow-slate-500/30',
          text: 'text-slate-300'
        };
    }
  };

  // Create the winding road path
  const RoadPath = () => {
    const pathPoints = [
      { x: 10, y: 15 },   // Start
      { x: 25, y: 25 },   // Curve right
      { x: 45, y: 20 },   // Milestone 1
      { x: 65, y: 35 },   // Curve down
      { x: 75, y: 50 },   // Milestone 2
      { x: 65, y: 65 },   // Curve left
      { x: 45, y: 70 },   // Milestone 3
      { x: 25, y: 65 },   // Curve up
      { x: 15, y: 50 },   // Milestone 4
      { x: 25, y: 35 },   // Curve right
      { x: 45, y: 30 },   // Milestone 5
      { x: 65, y: 45 },   // End curve
      { x: 85, y: 55 },   // Milestone 6
    ];

    // Create SVG path
    const createPath = () => {
      let path = `M ${pathPoints[0].x} ${pathPoints[0].y}`;
      for (let i = 1; i < pathPoints.length; i++) {
        const point = pathPoints[i];
        const prevPoint = pathPoints[i - 1];
        const controlX = (prevPoint.x + point.x) / 2;
        const controlY = prevPoint.y;
        path += ` Q ${controlX} ${controlY} ${point.x} ${point.y}`;
      }
      return path;
    };

    return (
      <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 100 80">
        {/* Road shadow */}
        <path
          d={createPath()}
          stroke="rgba(0, 0, 0, 0.3)"
          strokeWidth="1.2"
          fill="none"
          transform="translate(0.5, 0.5)"
        />
        {/* Main road */}
        <path
          d={createPath()}
          stroke="url(#roadGradient)"
          strokeWidth="1"
          fill="none"
          strokeDasharray="2 1"
          className="animate-pulse"
        />
        {/* Road glow */}
        <path
          d={createPath()}
          stroke="rgba(59, 130, 246, 0.4)"
          strokeWidth="1.5"
          fill="none"
          filter="blur(1px)"
        />
        
        {/* Gradient definition */}
        <defs>
          <linearGradient id="roadGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="50%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#f59e0b" />
          </linearGradient>
        </defs>
      </svg>
    );
  };

  // Milestone positions along the road
  const milestonePositions = [
    { x: 45, y: 20 },   // Step 1
    { x: 75, y: 50 },   // Step 2
    { x: 45, y: 70 },   // Step 3
    { x: 15, y: 50 },   // Step 4
    { x: 45, y: 30 },   // Step 5
    { x: 85, y: 55 },   // Step 6
  ];

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
    >
      <DialogContent className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-slate-700 max-w-7xl max-h-[95vh] w-[95vw] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-amber-400 bg-clip-text text-transparent flex items-center gap-3">
            <MapPin className="h-8 w-8 text-blue-400" />
            Python Learning Roadmap
            <ArrowRight className="h-6 w-6 text-purple-400" />
          </DialogTitle>
          <p className="text-slate-400 mt-2 text-lg">
            Transform your training plan with this step-by-step journey to Python mastery
          </p>
        </DialogHeader>

        <div className="relative w-full mt-8">
          <div className="relative w-full h-[800px] border-2 border-slate-600 rounded-2xl bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800 p-8 overflow-hidden shadow-2xl">
            {/* Background decorative elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full blur-xl"></div>
              <div className="absolute top-32 right-20 w-16 h-16 bg-purple-500 rounded-full blur-xl"></div>
              <div className="absolute bottom-20 left-32 w-24 h-24 bg-amber-500 rounded-full blur-xl"></div>
              <div className="absolute bottom-32 right-10 w-18 h-18 bg-emerald-500 rounded-full blur-xl"></div>
            </div>

            {/* Road path */}
            <RoadPath />

            {/* Roadmap Milestones */}
            {roadmapMilestones.slice(0, Math.min(chapters.length, 6)).map((milestone, index) => {
              const chapter = chapters[index];
              const position = milestonePositions[index];
              const isSelected = chapter?.id === selectedChapter?.id;
              const status = chapter?.status || 'locked';
              const style = getMilestoneStyle(status, isSelected);
              
              return (
                <div
                  key={milestone.step}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
                  style={{
                    left: `${position.x}%`,
                    top: `${position.y}%`,
                  }}
                >
                  {/* Milestone Container */}
                  <div
                    className={`relative transition-all duration-300 ${
                      status !== 'locked' ? 'cursor-pointer hover:scale-105' : ''
                    }`}
                    onClick={() => {
                      if (status !== 'locked' && chapter) {
                        setSelectedChapter(chapter);
                      }
                    }}
                  >
                    {/* Step Number Circle */}
                    <div
                      className={`w-16 h-16 rounded-full flex items-center justify-center border-4 shadow-xl transition-all duration-300 ${
                        style.bg
                      } ${
                        style.border
                      } relative z-10`}
                    >
                      {/* Pulse animation for available steps */}
                      {status === 'available' && (
                        <div className="absolute inset-0 rounded-full bg-blue-400/30 animate-ping"></div>
                      )}
                      
                      {/* Step number or icon */}
                      <div className={`text-2xl font-bold ${style.text} relative z-10`}>
                        {status === 'completed' ? (
                          <CheckCircle className="h-8 w-8" />
                        ) : status === 'locked' ? (
                          <Lock className="h-6 w-6" />
                        ) : (
                          milestone.step
                        )}
                      </div>
                    </div>

                    {/* Milestone Info Card */}
                    <div className="absolute top-20 left-1/2 transform -translate-x-1/2 w-80 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none group-hover:pointer-events-auto z-20">
                      <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-600 rounded-xl p-4 shadow-2xl">
                        {/* Step Header */}
                        <div className="flex items-center gap-3 mb-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${style.bg} ${style.text}`}>
                            {milestone.step}
                          </div>
                          <h3 className="text-lg font-bold text-white">
                            {milestone.title}
                          </h3>
                        </div>
                        
                        {/* Description */}
                        <p className="text-slate-300 text-sm mb-4 leading-relaxed">
                          {milestone.description}
                        </p>
                        
                        {/* Skills List */}
                        <div className="space-y-2">
                          <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">
                            You'll Learn:
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {milestone.skills.map((skill, skillIndex) => (
                              <span
                                key={skillIndex}
                                className="px-2 py-1 bg-slate-700/50 text-slate-300 text-xs rounded-md border border-slate-600"
                              >
                                {skill}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        {/* XP Badge */}
                        {chapter && (
                          <div className="flex items-center justify-between mt-4 pt-3 border-t border-slate-600">
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 text-amber-400" />
                              <span className="text-sm font-semibold text-amber-400">
                                {chapter.xp} XP
                              </span>
                            </div>
                            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                              status === 'completed'
                                ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                                : status === 'available'
                                ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                                : 'bg-slate-500/20 text-slate-400 border border-slate-500/30'
                            }`}>
                              {status === 'completed' ? 'Completed' : 
                               status === 'available' ? 'Available' : 'Locked'}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Selected Milestone Details */}
        {selectedChapter && (
          <div className="mt-8 p-8 border-2 border-slate-600 rounded-2xl bg-gradient-to-br from-slate-800/50 via-slate-700/50 to-slate-800/50 backdrop-blur-sm shadow-2xl">
            {(() => {
              const milestone = roadmapMilestones.find(m => m.step === selectedChapter.id);
              const style = getMilestoneStyle(selectedChapter.status, true);
              
              return (
                <>
                  {/* Header */}
                  <div className="flex items-start gap-6 mb-6">
                    <div className={`w-20 h-20 rounded-2xl flex items-center justify-center border-4 shadow-xl ${style.bg} ${style.border}`}>
                      <div className={`text-3xl font-bold ${style.text}`}>
                        {selectedChapter.status === 'completed' ? (
                          <CheckCircle className="h-10 w-10" />
                        ) : selectedChapter.status === 'locked' ? (
                          <Lock className="h-8 w-8" />
                        ) : (
                          selectedChapter.id
                        )}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-3xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                          {selectedChapter.title}
                        </h3>
                        <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                          selectedChapter.status === 'completed'
                            ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                            : selectedChapter.status === 'available'
                            ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                            : 'bg-slate-500/20 text-slate-400 border border-slate-500/30'
                        }`}>
                          {selectedChapter.status === 'completed' ? 'Completed' : 
                           selectedChapter.status === 'available' ? 'Available' : 'Locked'}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-slate-400">
                        <span className="text-lg">Step {selectedChapter.id} of 6</span>
                        <div className="flex items-center gap-2">
                          <Star className="h-5 w-5 text-amber-400" />
                          <span className="text-lg font-semibold text-amber-400">
                            {selectedChapter.xp} XP
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Description and Skills */}
                  {milestone && (
                    <div className="grid md:grid-cols-2 gap-8 mb-8">
                      <div>
                        <h4 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                          <Book className="h-5 w-5 text-blue-400" />
                          What You'll Learn
                        </h4>
                        <p className="text-slate-300 text-lg leading-relaxed">
                          {milestone.description}
                        </p>
                      </div>
                      
                      <div>
                        <h4 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                          <Trophy className="h-5 w-5 text-amber-400" />
                          Key Skills
                        </h4>
                        <div className="grid grid-cols-2 gap-3">
                          {milestone.skills.map((skill, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2 p-3 bg-slate-700/50 rounded-lg border border-slate-600"
                            >
                              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                              <span className="text-slate-300 font-medium">{skill}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Button */}
                  {selectedChapter.status !== 'locked' && (
                    <div className="flex justify-center">
                      <Link
                        href={`/courses/python/chapter/${selectedChapter.id}` as Route}
                        className={`inline-flex items-center gap-3 px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl ${
                          selectedChapter.status === 'completed'
                            ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700 shadow-emerald-500/25'
                            : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-blue-500/25'
                        }`}
                      >
                        {selectedChapter.status === 'completed' ? (
                          <>
                            <Book className="h-6 w-6" />
                            Review This Step
                            <ArrowRight className="h-5 w-5" />
                          </>
                        ) : (
                          <>
                            <MapPin className="h-6 w-6" />
                            Start This Step
                            <ArrowRight className="h-5 w-5" />
                          </>
                        )}
                      </Link>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PythonMapPopup;
