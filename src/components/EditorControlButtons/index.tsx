import { Button } from '@/components';
import { cn } from '@/utils';
import {
  BookOpenText,
  Loader,
  Play,
  RefreshCcw,
  SendHorizontal,
} from 'lucide-react';

type Props = {
  onSubmit: () => void;
  onRun: () => void;
  onResetCode: () => void;
  onSolutionReveal: () => void;
  showSolution: boolean;
  codeIsRunning: boolean;
};

const EditorControlButtons = ({
  onSubmit,
  onRun,
  onResetCode,
  onSolutionReveal,
  showSolution,
  codeIsRunning,
}: Props) => {
  const buttonClass = cn(
    'flex items-center m-1 shrink-0 cursor-pointer hover:shadow'
  );
  // TODO cant run code until pyodide is loaded
  return (
    <div className="flex flex-wrap gap-1 px-3 py-2 text-sm">
      <Button
        className={buttonClass}
        onClick={onSubmit}
        variant={'default'}
        size={'sm'}
      >
        <SendHorizontal
          size={20}
          strokeWidth={1.5}
          className="mr-1"
        />
        Submit
      </Button>

      <Button
        className={buttonClass}
        onClick={onRun}
        variant={'outline'}
        size={'sm'}
        disabled={codeIsRunning}
      >
        <span className={'mr-1'}>
          {codeIsRunning ? (
            <Loader
              size={20}
              strokeWidth={1.5}
              className="animate-spin"
            />
          ) : (
            <Play
              size={20}
              strokeWidth={1.5}
            />
          )}
        </span>
        <span>{codeIsRunning ? 'Running...' : 'Run'}</span>
      </Button>

      <Button
        className={buttonClass}
        onClick={onSolutionReveal}
        variant={showSolution ? 'default' : 'outline'}
        size={'sm'}
      >
        <BookOpenText
          size={20}
          strokeWidth={1.5}
          className={'mr-1'}
        />
        <span>Solution</span>
      </Button>

      <Button
        className={buttonClass}
        onClick={onResetCode}
        variant={'destructive'}
        size={'sm'}
      >
        <RefreshCcw
          size={20}
          strokeWidth={1.5}
          className="mr-1"
        />
        <span>Reset</span>
      </Button>
    </div>
  );
};

export { EditorControlButtons };
