'use client';

import { ReactNode, useRef, useState } from 'react';
import { useCopyToClipboard } from '@uidotdev/usehooks';
import { ClipboardCheck, Copy } from 'lucide-react';
import { toast } from 'sonner';

const MDXCode = ({
  children,
  classname,
}: {
  children: ReactNode;
  classname: string;
}) => {
  const codeRef = useRef<HTMLElement | null>(null);
  const [, copy] = useCopyToClipboard();
  const [copyIsSuccessfull, setCopyIsSuccessfull] = useState(false);

  const copyToClipboard = (text: string) => {
    copy(text);

    toast('Copied to clipboard!');

    setCopyIsSuccessfull(true);

    setTimeout(() => setCopyIsSuccessfull(false), 3000);
  };

  return (
    <code
      className={classname}
      ref={codeRef}
    >
      <span>{children}</span>

      <span
        className="absolute right-0 top-0 inline-block cursor-pointer p-2 hover:bg-secondary/80"
        onClick={() =>
          copyToClipboard((codeRef?.current as HTMLElement)?.textContent ?? '')
        }
      >
        {copyIsSuccessfull ? (
          <ClipboardCheck
            size={20}
            strokeWidth={1}
          />
        ) : (
          <Copy
            size={20}
            strokeWidth={1}
          />
        )}
      </span>
    </code>
  );
};

export { MDXCode };
