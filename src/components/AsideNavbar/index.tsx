import Link from 'next/link';
import {
  <PERSON><PERSON>,
  Separator,
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components';
import { SignOutButton } from '@clerk/nextjs';
import {
  Bell,
  CircleUserRound,
  LogOut,
  Moon,
  Settings,
  Sun,
} from 'lucide-react';
import { useTheme } from 'next-themes';

const AsideNavbar = () => {
  const { theme, setTheme } = useTheme();
  return (
    <div className="flex min-h-screen w-12 flex-col content-between items-center border border-t-0 px-2">
      <nav className="flex flex-col content-between items-center justify-between">
        <>
          <TooltipProvider
            delayDuration={400}
            skipDelayDuration={200}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant={'ghost'}>
                  <Bell
                    size={20}
                    strokeWidth={1.5}
                  />
                </Button>
              </TooltipTrigger>

              <TooltipContent side="right">
                <p>Notifications</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator />

          <TooltipProvider
            delayDuration={400}
            skipDelayDuration={200}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant={'ghost'}>
                  <Link href="/profile">
                    <CircleUserRound
                      size={20}
                      strokeWidth={1.5}
                    />
                  </Link>
                </Button>
              </TooltipTrigger>

              <TooltipContent side="right">
                <p>Go to profile page</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator />

          <TooltipProvider
            delayDuration={400}
            skipDelayDuration={200}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant={'ghost'}>
                  <Link href="/settings">
                    <Settings
                      size={20}
                      strokeWidth={1.5}
                    />
                  </Link>
                </Button>
              </TooltipTrigger>

              <TooltipContent side="right">
                <p>Go to settings</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator />

          <TooltipProvider
            delayDuration={400}
            skipDelayDuration={200}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="cursor-pointer"
                  onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                  variant="ghost"
                >
                  {theme === 'dark' ? (
                    <Sun
                      size={20}
                      strokeWidth={1.5}
                      className="size-[1.2rem]"
                    />
                  ) : (
                    <Moon
                      size={20}
                      strokeWidth={1.5}
                      className="size-[1.2rem]"
                    />
                  )}

                  <span className="sr-only">Toggle theme</span>
                </Button>
              </TooltipTrigger>

              <TooltipContent side="right">
                <p>
                  {theme === 'dark'
                    ? 'Turn light mode on'
                    : 'Turn dark mode on'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator />

          <TooltipProvider
            delayDuration={400}
            skipDelayDuration={200}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <SignOutButton>
                  <Button variant={'ghost'}>
                    <LogOut
                      size={20}
                      strokeWidth={1.5}
                    />
                  </Button>
                </SignOutButton>
              </TooltipTrigger>

              <TooltipContent side="right">
                <p>Log out</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator />
        </>
      </nav>
    </div>
  );
};

export { AsideNavbar };
