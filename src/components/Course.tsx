import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { auth } from '@clerk/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import {
  BookOpen,
  CheckCircle2,
  ChevronRight,
  Lock,
  PlayCircle,
} from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/accordion';

interface CourseProps {
  course: Doc<'courses'>;
  chapters: Doc<'chapters'>[];
  lessons: Doc<'lessons'>[];
}

const Course = async ({ course, chapters, lessons }: CourseProps) => {
  const authObj = await auth();
  const { userId } = authObj;

  const progress = await fetchQuery(api.user_progress.getUserProgress, {
    user_id: userId ?? '',
    course_id: course._id,
  });

  const completedLessons = progress?.filter(p => p.is_completed)?.length ?? 0;
  const progressPercentage =
    lessons.length > 0
      ? Math.round((completedLessons / lessons.length) * 100)
      : 0;

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-4xl mx-auto">
          {/* Course Header */}
          <div className="mb-12">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2.5 rounded bg-gruvbox-green-dark/10">
                <BookOpen className="h-6 w-6 text-gruvbox-green-dark" />
              </div>
              <h1 className="text-4xl font-mono font-bold text-gruvbox-red">
                {course.name}
              </h1>
            </div>
            {/* <p className="text-lg font-mono text-gruvbox-bg-gray mb-6">
              {course?.description ?? ''}
            </p> */}
            <div className="flex items-center gap-3 font-mono text-gruvbox-green-dark">
              <div className="flex items-center gap-2">
                <PlayCircle className="h-5 w-5" />
                <span>{lessons.length} lessons</span>
              </div>
              <span className="text-gruvbox-green-dark/50">•</span>
              <span>{progressPercentage}% complete</span>
            </div>
          </div>

          {/* Course Content */}
          <div className="space-y-4">
            {chapters.map(chapter => {
              const chapterLessons = lessons.filter(
                lesson => lesson.chapter_id === chapter._id
              );

              return (
                <div
                  key={chapter._id}
                  className="bg-gruvbox-bg-darker border border-gruvbox-bg-darker rounded-md overflow-hidden"
                >
                  <Accordion
                    type="single"
                    collapsible
                  >
                    <AccordionItem
                      value={chapter._id}
                      className="border-none"
                    >
                      <AccordionTrigger className="px-6 py-4 hover:bg-gruvbox-green-dark/5 hover:no-underline data-[state=open]:bg-gruvbox-green-dark/5">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded bg-gruvbox-green-dark/10">
                            <PlayCircle className="h-5 w-5 text-gruvbox-green-dark" />
                          </div>
                          <div className="text-left">
                            <h3 className="font-mono font-bold text-white text-lg">
                              {chapter.name}
                            </h3>
                            <p className="text-sm font-mono text-gruvbox-bg-gray">
                              {chapterLessons.length} lessons
                            </p>
                          </div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="border-t border-gruvbox-fg-dark">
                        <div className="divide-y divide-gruvbox-fg-dark">
                          {chapterLessons.map(lesson => {
                            const isCompleted = progress?.some(
                              p => p.lesson_id === lesson._id && p.is_completed
                            );

                            return (
                              <Link
                                key={lesson._id}
                                href={`/${course.slug}/${lesson.slug}`}
                                className="flex items-center justify-between px-6 py-4 hover:bg-gruvbox-green-dark/5 transition-colors group"
                              >
                                <div className="flex items-center gap-3">
                                  {isCompleted ? (
                                    <CheckCircle2 className="h-5 w-5 text-gruvbox-green-dark" />
                                  ) : (
                                    <Lock className="h-5 w-5 text-gruvbox-bg-gray group-hover:text-gruvbox-green-dark" />
                                  )}
                                  <span className="font-mono text-white group-hover:text-gruvbox-green-dark">
                                    {lesson.name}
                                  </span>
                                </div>
                                <ChevronRight className="h-5 w-5 text-gruvbox-bg-gray group-hover:text-gruvbox-green-dark" />
                              </Link>
                            );
                          })}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Course;
