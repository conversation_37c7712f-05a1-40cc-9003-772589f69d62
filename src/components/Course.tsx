import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { auth } from '@clerk/nextjs/server';
import { fetchQuery } from 'convex/nextjs';
import {
  Book<PERSON>pen,
  CheckCircle2,
  ChevronRight,
  Layers,
  Lock,
  PlayCircle,
} from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/shadcn/accordion';
import { PythonCourseMap } from '@/components/PythonCourseMap';

interface CourseProps {
  course: Doc<'courses'>;
  chapters: Doc<'chapters'>[];
  lessons: Doc<'lessons'>[];
}

// Mock descriptions for chapters
const getChapterDescription = (chapterIndex: number): string => {
  const descriptions = [
    "Master the fundamentals of Python programming with variables, data types, and basic operations.",
    "Learn to control program flow using conditional statements and logical operators.",
    "Understand iteration patterns with for loops, while loops, and list comprehensions.",
    "Explore Python's built-in data structures: lists, tuples, dictionaries, and sets.",
    "Create reusable code with functions, parameters, return values, and scope concepts.",
    "Dive into object-oriented programming with classes, objects, inheritance, and polymorphism.",
    "Handle errors gracefully using try-catch blocks and custom exception handling.",
    "Work with files, directories, and external data sources in Python applications.",
  ];
  return descriptions[chapterIndex] || "Explore advanced Python concepts and practical applications.";
};

// Mock descriptions for lessons
const getLessonDescription = (chapterIndex: number, lessonIndex: number): string => {
  const lessonDescriptions = [
    // Chapter 0 - Fundamentals
    [
      "Learn about Python syntax, variables, and how to store data in your programs.",
      "Understand different data types like strings, integers, floats, and booleans.",
      "Master basic arithmetic and string operations in Python programming.",
      "Practice variable assignment and naming conventions in Python.",
    ],
    // Chapter 1 - Control Flow
    [
      "Learn how to make decisions in your code using if-else statements.",
      "Understand comparison operators and how to compare values effectively.",
      "Master logical operators like and, or, and not for complex conditions.",
      "Practice nested conditionals and elif statements for multiple conditions.",
    ],
    // Chapter 2 - Loops
    [
      "Understand how to repeat code execution using for loops and iteration.",
      "Learn while loops for conditional repetition and avoiding infinite loops.",
      "Master list comprehensions for elegant and efficient data processing.",
      "Practice nested loops and loop control statements like break and continue.",
    ],
    // Chapter 3 - Data Structures
    [
      "Learn to work with lists: creation, indexing, slicing, and modification.",
      "Understand tuples as immutable sequences and their use cases.",
      "Master dictionaries for key-value pair storage and efficient data lookup.",
      "Explore sets for unique collections and set operations like union and intersection.",
    ],
    // Chapter 4 - Functions
    [
      "Learn to define functions with parameters and return values for code reusability.",
      "Understand function scope, local vs global variables, and the LEGB rule.",
      "Master advanced function concepts like default parameters and keyword arguments.",
      "Practice lambda functions and higher-order functions for functional programming.",
    ],
    // Chapter 5 - OOP
    [
      "Learn to create classes and objects to model real-world entities in code.",
      "Understand inheritance and how to create specialized classes from base classes.",
      "Master polymorphism and method overriding for flexible object behavior.",
      "Practice encapsulation and data hiding using private attributes and methods.",
    ],
    // Chapter 6 - Error Handling
    [
      "Learn to handle runtime errors gracefully using try-except blocks.",
      "Understand different exception types and how to catch specific errors.",
      "Master finally blocks and cleanup code that always executes.",
      "Practice creating custom exceptions for domain-specific error handling.",
    ],
    // Chapter 7 - File Operations
    [
      "Learn to read from and write to files using Python's built-in file operations.",
      "Understand different file modes and how to work with text and binary files.",
      "Master CSV and JSON file processing for structured data handling.",
      "Practice working with file paths and directory operations using os and pathlib.",
    ],
  ];
  
  const chapterLessons = lessonDescriptions[chapterIndex] || [];
  return chapterLessons[lessonIndex] || "Practice and apply the concepts learned in this comprehensive lesson.";
};

const Course = async ({ course, chapters, lessons }: CourseProps) => {
  const authObj = await auth();
  const { userId } = authObj;

  const progress = await fetchQuery(api.user_progress.getUserProgress, {
    user_id: userId ?? '',
    course_id: course._id,
  });

  const completedLessons = progress?.filter(p => p.is_completed)?.length ?? 0;
  const progressPercentage =
    lessons.length > 0
      ? Math.round((completedLessons / lessons.length) * 100)
      : 0;

  return (
    <div>
      {/* Course Header */}
      <div className="mb-12">
        <h1 className="text-4xl font-mono font-bold mb-3 text-white">
          {course.name}
        </h1>
        <p className="text-lg font-mono text-gruvbox-bg-gray mb-6">
          Master Python programming with hands-on lessons and interactive coding exercises
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 font-mono text-gruvbox-green-dark">
            <div className="flex items-center gap-2">
              <PlayCircle className="h-5 w-5" />
              <span>{lessons.length} lessons</span>
            </div>
            <span className="text-gruvbox-green-dark/50">•</span>
            <span>{progressPercentage}% complete</span>
          </div>
          <PythonCourseMap
            chapters={chapters}
            lessons={lessons}
            progress={progress}
          />
        </div>
      </div>

      {/* Course Content */}
      <div className="space-y-8">
        {chapters.map((chapter, chapterIndex) => {
          const chapterLessons = lessons.filter(
            lesson => lesson.chapter_id === chapter._id
          );

          const completedChapterLessons = chapterLessons.filter(lesson =>
            progress?.some(
              p => p.lesson_id === lesson._id && p.is_completed
            )
          ).length;

          const chapterProgress =
            chapterLessons.length > 0
              ? Math.round(
                  (completedChapterLessons / chapterLessons.length) * 100
                )
              : 0;

          return (
                <div
                  key={chapter._id}
                  className="group relative bg-gradient-to-br from-gruvbox-bg1/60 to-gruvbox-bg-darker/80 backdrop-blur-sm border border-gruvbox-bg3/30 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gruvbox-green-dark/40"
                >
                  {/* Subtle gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-gruvbox-green-dark/5 via-transparent to-gruvbox-blue-dark/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <Accordion
                    type="single"
                    collapsible
                    defaultValue={chapterIndex === 0 ? chapter._id : undefined}
                  >
                    <AccordionItem
                      value={chapter._id}
                      className="border-none"
                    >
                      <AccordionTrigger className="relative px-10 py-8 hover:bg-gruvbox-green-dark/8 hover:no-underline data-[state=open]:bg-gruvbox-green-dark/10 transition-all duration-200 [&>svg]:h-8 [&>svg]:w-8">
                        <div className="flex items-center gap-6 w-full">
                          {/* Chapter Icon with Progress Ring */}
                          <div className="relative">
                            <div className="p-4 rounded-xl bg-gradient-to-br from-gruvbox-green-dark/20 to-gruvbox-green-dark/10 border border-gruvbox-green-dark/20">
                              <Layers className="h-7 w-7 text-gruvbox-green-dark" />
                            </div>
                            {chapterProgress > 0 && (
                              <div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-gruvbox-green-dark flex items-center justify-center">
                                <span className="text-xs font-bold text-gruvbox-bg-dark">
                                  {chapterProgress}%
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Chapter Info */}
                          <div className="text-left flex-1">
                            <div className="flex items-center gap-4 mb-2">
                              <h3 className="font-mono font-bold text-gruvbox-fg-dark text-xl group-hover:text-gruvbox-green-light transition-colors">
                                {chapter.name}
                              </h3>
                              <span className="px-3 py-1 text-xs font-mono bg-gruvbox-bg3/50 text-gruvbox-fg2 rounded-md">
                                Chapter {chapterIndex + 1}
                              </span>
                            </div>
                            <p className="text-sm text-gruvbox-fg2 mb-3 font-mono">
                              {getChapterDescription(chapterIndex)}
                            </p>
                            <div className="flex items-center gap-6 text-sm font-mono text-gruvbox-fg2">
                              <span className="flex items-center gap-2">
                                <BookOpen className="h-4 w-4" />
                                {chapterLessons.length} lessons
                              </span>
                              <span className="flex items-center gap-2">
                                <CheckCircle2 className="h-4 w-4" />
                                {completedChapterLessons} completed
                              </span>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="hidden sm:flex flex-col items-end gap-3">
                            <span className="text-sm font-mono text-gruvbox-green-dark font-semibold">
                              {chapterProgress}%
                            </span>
                            <div className="w-28 h-2 bg-gruvbox-bg3/50 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-gradient-to-r from-gruvbox-green-dark to-gruvbox-green-light transition-all duration-500"
                                style={{ width: `${chapterProgress}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="border-t border-gruvbox-bg3/30 bg-gruvbox-bg-darker/40">
                        <div className="p-6">
                          <div className="grid gap-4">
                            {chapterLessons.map((lesson, lessonIndex) => {
                              const isCompleted = progress?.some(
                                p =>
                                  p.lesson_id === lesson._id && p.is_completed
                              );

                              return (
                                <Link
                                  key={lesson._id}
                                  href={`/${course.slug}/${lesson.slug}`}
                                  className="group/lesson relative flex items-center gap-5 p-5 rounded-lg bg-gruvbox-bg1/30 hover:bg-gruvbox-bg1/50 border border-gruvbox-bg3/20 hover:border-gruvbox-green-dark/30 transition-all duration-200 hover:shadow-md"
                                >
                                  {/* Lesson Number & Status */}
                                  <div className="flex items-center gap-4">
                                    <div
                                      className={`w-9 h-9 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-200 ${
                                        isCompleted
                                          ? 'bg-gruvbox-green-dark text-gruvbox-bg-dark'
                                          : 'bg-gruvbox-bg3/50 text-gruvbox-fg2 group-hover/lesson:bg-gruvbox-green-dark/20 group-hover/lesson:text-gruvbox-green-dark'
                                      }`}
                                    >
                                      {isCompleted ? (
                                        <CheckCircle2 className="h-5 w-5" />
                                      ) : (
                                        <span>{lessonIndex + 1}</span>
                                      )}
                                    </div>

                                    {!isCompleted && (
                                      <Lock className="h-5 w-5 text-gruvbox-bg-gray group-hover/lesson:text-gruvbox-green-dark transition-colors" />
                                    )}
                                  </div>

                                  {/* Lesson Content */}
                                  <div className="flex-1">
                                    <h4 className="font-mono font-semibold text-gruvbox-fg-dark group-hover/lesson:text-gruvbox-green-light transition-colors">
                                      {lesson.name}
                                    </h4>
                                    <p className="text-sm text-gruvbox-fg2 mt-2 line-clamp-2">
                                      {getLessonDescription(chapterIndex, lessonIndex)}
                                    </p>
                                  </div>

                                  {/* Action Arrow */}
                                  <ChevronRight className="h-6 w-6 text-gruvbox-bg-gray group-hover/lesson:text-gruvbox-green-dark group-hover/lesson:translate-x-1 transition-all duration-200" />

                                  {/* Completion Badge */}
                                  {isCompleted && (
                                    <div className="absolute top-3 right-3 px-2 py-1 text-xs font-mono bg-gruvbox-green-dark/20 text-gruvbox-green-dark rounded-md">
                                      Completed
                                    </div>
                                  )}
                                </Link>
                              );
                            })}
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              );
            })}
      </div>
    </div>
  );
};

export default Course;
