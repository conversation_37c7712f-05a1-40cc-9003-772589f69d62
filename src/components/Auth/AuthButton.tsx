'use client';

import { SignInButton, SignUp<PERSON>utton, UserButton, useUser } from '@clerk/nextjs';

import { Button } from '../../shadcn/button';
import { Skeleton } from '../../shadcn/skeleton';

export const AuthButton = () => {
  const { isSignedIn, isLoaded } = useUser();

  if (!isLoaded) {
    return (
      <div className="flex gap-2">
        <Skeleton className="h-10 w-[78px] rounded-md" />
      </div>
    );
  }

  if (isSignedIn) {
    return <UserButton />;
  }

  return (
    <div className="flex gap-2">
      <SignInButton mode="modal">
        <Button variant="outline">Sign in</Button>
      </SignInButton>

      <SignUpButton mode="modal">
        <Button>Sign up</Button>
      </SignUpButton>
    </div>
  );
};
