import { JSXElementConstructor, memo, ReactElement } from 'react';
import { ReportIssue } from '@/components';

import AdminEditLesson from '../AdminEditLessonLink';
import ProtectedAdmin from '../ProtectedAdmin';

type Props = {
  mdxComponents: ReactElement<unknown, string | JSXElementConstructor<unknown>>;
};

const compare = (prevProps: Props, nextProps: Props) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
};

const LessonContent = memo(({ mdxComponents }: Props) => {
  return (
    <article className="prose prose-slate mb-[4.25rem] min-w-full max-w-none px-4 py-2 dark:prose-invert">
      <div className="mb-2 flex justify-end">
        <ProtectedAdmin>
          <AdminEditLesson />
        </ProtectedAdmin>

        <ReportIssue />
      </div>

      {mdxComponents}
    </article>
  );
}, compare);

LessonContent.displayName = 'LessonContent';

export { LessonContent };
