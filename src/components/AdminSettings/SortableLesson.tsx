import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useMutation } from 'convex/react';
import { GripVertical, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/shadcn/button';
import { Checkbox } from '@/shadcn/checkbox';

import { LessonOrderControls } from './LessonOrderControls';

type Props = {
  lesson: Doc<'lessons'>;
  chapterLessons: Doc<'lessons'>[];
  index: number;
  isReordering?: boolean;
  onDelete: () => void;
  extraActions?: React.ReactNode;
};

export const SortableLesson = ({
  lesson,
  chapterLessons,
  index,
  isReordering,
  onDelete,
  extraActions,
}: Props) => {
  const patchLesson = useMutation(api.lessons.patchLesson);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: lesson._id,
    data: {
      type: 'lesson',
      lesson,
    },
    disabled: isReordering,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging || isReordering ? 0.5 : 1,
  };

  const handlePublishToggle = async (checked: boolean) => {
    try {
      await patchLesson({
        _id: lesson._id,
        is_published: checked,
      });
      toast.success(
        `Lesson ${checked ? 'published' : 'unpublished'} successfully`
      );
    } catch (error) {
      console.error('Failed to update lesson publish state:', error);
      toast.error('Failed to update lesson publish state');
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`flex items-center gap-2 p-2 rounded-md border ${
        isDragging ? 'opacity-50' : ''
      } ${isReordering ? 'cursor-not-allowed' : ''}`}
    >
      <Button
        variant="ghost"
        size="icon"
        className={`h-8 w-8 ${isReordering ? 'cursor-not-allowed' : 'cursor-move'}`}
        {...listeners}
      >
        <GripVertical className="h-4 w-4" />
      </Button>
      <Checkbox
        checked={lesson.is_published}
        onCheckedChange={handlePublishToggle}
        disabled={isReordering}
        aria-label={`Toggle publish state for ${lesson.name}`}
      />

      <span className="flex-1">
        <span className="text-muted-foreground mr-2">{lesson.order}.</span>
        {lesson.name}
      </span>

      <div className="flex items-center gap-1">
        <LessonOrderControls
          lesson={lesson}
          chapterLessons={chapterLessons}
          index={index}
        />

        {extraActions}
        <Button
          variant="ghost"
          size="icon"
          onClick={onDelete}
          className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
