import { ChangeEvent, useEffect, useRef, useState } from 'react';
import { api } from '@/convex/_generated/api';
import { Doc, Id } from '@/convex/_generated/dataModel';
import { cn } from '@/utils';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useMutation } from 'convex/react';
import { Check, Edit as EditIcon, GripVertical, Trash2, X } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/shadcn/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shadcn/card';
import { Input } from '@/shadcn/input';

import { ChapterOrderControls } from './ChapterOrderControls';
import { SortableLesson } from './SortableLesson';

type Props = {
  chapter: Doc<'chapters'>;
  lessons?: Doc<'lessons'>[];
  courseChapters: Doc<'chapters'>[];
  index: number;
  onDeleteChapter: () => void;
  onDeleteLesson: (lessonId: Id<'lessons'>) => void;
  renderLessonActions?: (lesson: Doc<'lessons'>) => React.ReactNode;
};

export function SortableChapter({
  chapter,
  lessons = [],
  courseChapters,
  index,
  onDeleteChapter,
  onDeleteLesson,
  renderLessonActions,
}: Props) {
  const [isReordering, setIsReordering] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [chapterName, setChapterName] = useState(chapter.name);
  const inputRef = useRef<HTMLInputElement>(null);

  const updateLessonOrder = useMutation(api.lessons.updateLessonOrder);
  const updateChapter = useMutation(api.chapters.updateChapter);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleSaveChapterName = async () => {
    if (chapterName.trim() === '') {
      toast.error('Chapter name cannot be empty');
      return;
    }

    try {
      await updateChapter({
        chapter_id: chapter._id,
        name: chapterName.trim(),
      });
      setIsEditing(false);
      toast.success('Chapter name updated');
    } catch (error) {
      console.error('Failed to update chapter name:', error);
      toast.error('Failed to update chapter name');
    }
  };

  const handleCancelEdit = () => {
    setChapterName(chapter.name);
    setIsEditing(false);
  };

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: chapter._id,
    data: {
      type: 'chapter',
      chapter,
    },
    disabled: isReordering,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (
      active.data.current?.type === 'lesson' &&
      over?.data.current?.type === 'lesson' &&
      lessons
    ) {
      const activeLesson = active.data.current.lesson as Doc<'lessons'>;

      const overLesson = over.data.current.lesson as Doc<'lessons'>;

      // Only allow reordering within the same chapter
      if (activeLesson.chapter_id !== overLesson.chapter_id) {
        return;
      }

      const chapterLessons = lessons
        .filter(l => l.chapter_id === activeLesson.chapter_id)
        .sort((a, b) => a.order - b.order);

      const oldIndex = chapterLessons.findIndex(l => l._id === active.id);
      const newIndex = chapterLessons.findIndex(l => l._id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const reorderedLessons = arrayMove(chapterLessons, oldIndex, newIndex);

        try {
          await updateLessonOrder({
            lessons: reorderedLessons.map((lesson, index) => ({
              _id: lesson._id,
              order: index,
              chapter_id: activeLesson.chapter_id as Id<'chapters'>,
            })),
          });
        } catch (error) {
          console.error('Failed to update lesson order:', error);
        }
      }
    }
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`${isDragging ? 'z-50' : ''} ${isReordering ? 'cursor-not-allowed' : ''}`}
    >
      <CardHeader className="flex flex-row items-center justify-between p-4">
        <div className="flex items-center gap-2">
          <div
            {...attributes}
            {...listeners}
            className={cn(
              'cursor-grab',
              isDragging && 'cursor-grabbing',
              isReordering && 'cursor-not-allowed'
            )}
          >
            <GripVertical className="h-5 w-5 text-muted-foreground" />
          </div>

          {isEditing ? (
            <div className="flex items-center gap-2 flex-grow">
              <Input
                ref={inputRef}
                value={chapterName}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setChapterName(e.target.value)
                }
                className="h-8 min-w-[300px] w-full max-w-[500px]"
                onKeyDown={e => {
                  if (e.key === 'Enter') handleSaveChapterName();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
              />

              <Button
                variant="ghost"
                size="icon"
                onClick={handleSaveChapterName}
                className="h-8 w-8 text-green-600 hover:text-green-700 hover:bg-green-100"
              >
                <Check className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleCancelEdit}
                className="h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg break-words max-w-[500px]">
                {chapter.name}
              </CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsEditing(true)}
                className="h-8 w-8 text-primary hover:text-primary hover:bg-primary/10"
              >
                <EditIcon className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <ChapterOrderControls
            chapter={chapter}
            isFirst={index === 0}
            isLast={index === courseChapters.length - 1}
            courseChapters={courseChapters}
          />

          <Button
            variant="ghost"
            size="icon"
            onClick={onDeleteChapter}
            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div
          className={cn(
            isReordering ? 'pointer-events-none opacity-50' : 'opacity-100'
          )}
        >
          <DndContext onDragEnd={handleDragEnd}>
            <SortableContext
              items={lessons.map(l => l._id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-2">
                {lessons.map((lesson, index) => (
                  <SortableLesson
                    key={lesson._id}
                    lesson={lesson}
                    chapterLessons={lessons}
                    index={index}
                    isReordering={isReordering}
                    onDelete={() => onDeleteLesson(lesson._id)}
                    extraActions={
                      renderLessonActions
                        ? renderLessonActions(lesson)
                        : undefined
                    }
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      </CardContent>
    </Card>
  );
}
