import { useState } from 'react';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { useMutation } from 'convex/react';
import { ArrowDown, ArrowUp, Loader2 } from 'lucide-react';

import { Button } from '@/shadcn/button';

type Props = {
  chapter: Doc<'chapters'>;
  isFirst: boolean;
  isLast: boolean;
  courseChapters: Doc<'chapters'>[];
};

export function ChapterOrderControls({
  chapter,
  isFirst,
  isLast,
  courseChapters,
}: Props) {
  const updateChapterOrder = useMutation(api.chapters.updateChapterOrder);
  const [isReordering, setIsReordering] = useState(false);
  const [direction, setDirection] = useState<'up' | 'down' | null>(null);

  const handleMove = async (moveDirection: 'up' | 'down') => {
    setIsReordering(true);
    setDirection(moveDirection);

    const currentIndex = courseChapters.findIndex(ch => ch._id === chapter._id);

    try {
      const reorderedChapters = [...courseChapters];
      const movingChapter = reorderedChapters[currentIndex];
      const targetIndex =
        moveDirection === 'up' ? currentIndex - 1 : currentIndex + 1;

      reorderedChapters[currentIndex] = reorderedChapters[targetIndex];
      reorderedChapters[targetIndex] = movingChapter;

      await updateChapterOrder({
        chapters: reorderedChapters.map((ch, idx) => ({
          _id: ch._id,
          order: idx,
        })),
      });
    } catch (error) {
      console.error('Failed to reorder chapter:', error);
    } finally {
      setIsReordering(false);
      setDirection(null);
    }
  };

  return (
    <div className="flex gap-1">
      <Button
        variant="ghost"
        size="icon"
        disabled={isFirst || isReordering}
        onClick={() => handleMove('up')}
        className="h-8 w-8"
      >
        {isReordering && direction === 'up' ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <ArrowUp className="h-4 w-4" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="icon"
        disabled={isLast || isReordering}
        onClick={() => handleMove('down')}
        className="h-8 w-8"
      >
        {isReordering && direction === 'down' ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
