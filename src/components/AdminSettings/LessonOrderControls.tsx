import { useState } from 'react';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { useMutation } from 'convex/react';
import { ArrowDown, ArrowUp, Loader2 } from 'lucide-react';

import { Button } from '@/shadcn/button';

type Props = {
  lesson: Doc<'lessons'>;
  chapterLessons: Doc<'lessons'>[];
  index: number;
};

export const LessonOrderControls = ({
  lesson,
  chapterLessons,
  index,
}: Props) => {
  const reorderLesson = useMutation(api.lessons.reorderLesson);
  const [isReordering, setIsReordering] = useState(false);
  const [direction, setDirection] = useState<'up' | 'down' | null>(null);

  const isFirst = index === 0;
  const isLast = index === chapterLessons.length - 1;

  const handleMove = async (moveDirection: 'up' | 'down', index: number) => {
    setIsReordering(true);
    setDirection(moveDirection);

    const currentIndex = chapterLessons.findIndex(l => l._id === lesson._id);
    const newOrder = moveDirection === 'up' ? index - 1 : index + 1;

    try {
      await reorderLesson({
        lesson_id: lesson._id,
        target_chapter_id: lesson.chapter_id!,
        new_order: newOrder,
      });
    } catch (error) {
      console.error('Failed to reorder lesson:', error);
    } finally {
      setIsReordering(false);
      setDirection(null);
    }
  };

  return (
    <div className="flex gap-1">
      <Button
        variant="ghost"
        size="icon"
        disabled={isFirst || isReordering}
        onClick={() => handleMove('up', index)}
        className="h-8 w-8"
      >
        {isReordering && direction === 'up' ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <ArrowUp className="h-4 w-4" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="icon"
        disabled={isLast || isReordering}
        onClick={() => handleMove('down', index)}
        className="h-8 w-8"
      >
        {isReordering && direction === 'down' ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};
