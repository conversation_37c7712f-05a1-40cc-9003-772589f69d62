import { Doc } from '@/convex/_generated/dataModel';
import { cn } from '@/utils';

import { Button } from '@/shadcn/button';

type Props = {
  course: Doc<'courses'>;
  isSelected: boolean;
  onClick: () => void;
};

const CourseItem = ({ course, isSelected, onClick }: Props) => {
  return (
    <Button
      variant="ghost"
      className={cn(
        'w-full justify-start',
        isSelected && 'bg-primary/10 hover:bg-primary/20'
      )}
      onClick={onClick}
    >
      {course.name}
    </Button>
  );
};

export default CourseItem;
