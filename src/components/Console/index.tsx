'use client';

// import hljs from 'highlight.js/lib/core';
// import python from 'highlight.js/lib/languages/python';
// import parse from 'html-react-parser';
import { ScrollArea, ScrollBar } from '@/shadcn/scroll-area';
import { useIsClient } from '@uidotdev/usehooks';

type Props = {
  output: string;
};

const Console = ({ output = '' }: Props) => {
  const isClient = useIsClient();

  // const highlightedCode = '';

  if (isClient) {
    // hljs.registerLanguage('python', python);
    // highlightedCode = hljs.highlight(output, { language: 'python' }).value;
  }

  return (
    <div className="h-full min-w-full px-4 pb-[4.25rem] text-sm">
      <p className="text-base">Output:</p>
      <article className="h-full min-w-full overflow-hidden pb-2">
        <ScrollArea
          type="always"
          className="hljs size-full min-h-[200px] border p-4 shadow-sm"
        >
          <pre className="inline-block min-h-full w-auto min-w-full max-w-full text-pretty">
            <ScrollBar orientation="vertical" />
            <code className="inline-block size-full min-h-full min-w-[100px]">
              {/* {parse(highlightedCode)} */}
              {output}
            </code>

            <ScrollBar orientation="horizontal" />
          </pre>
        </ScrollArea>
      </article>
    </div>
  );
};

export { Console };
