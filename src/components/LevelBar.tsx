'use client';

import { useEffect, useState } from 'react';
import { cn } from '@/utils';
import { useUser } from '@clerk/nextjs';
import { useQuery } from 'convex/react';

import { Progress } from '@/shadcn/progress';

import { api } from '../../convex/_generated/api';

const calculateRequiredXP = (level: number) => {
  // Common RPG formula: next_level = current_level * 2.5 * 100
  return Math.floor(level * 2.5 * 100);
};

export const LevelBar = () => {
  const [progress, setProgress] = useState(0);
  const [isLevelingUp, setIsLevelingUp] = useState(false);
  const { user } = useUser();

  const userData = useQuery(api.users.getUserLevel, { userId: user?.id ?? '' });

  const level = userData?.level ?? 1;
  const currentXP = userData?.current_xp ?? 0;
  const requiredXP = calculateRequiredXP(level);
  const progressPercentage = (currentXP / requiredXP) * 100;

  useEffect(() => {
    // Animate progress bar
    setProgress(progressPercentage);
  }, [progressPercentage]);

  useEffect(() => {
    if (isLevelingUp) {
      const timer = setTimeout(() => setIsLevelingUp(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isLevelingUp]);

  if (!user) {
    return null;
  }

  return (
    <div className="w-full sm:w-48 md:w-64">
      <div className={'flex flex-col gap-1 w-full'}>
        {/* Desktop and tablet view */}
        <div className="hidden sm:flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div
              className={cn(
                'flex items-center justify-center w-8 h-8 rounded-full bg-accent text-primary-foreground font-bold',
                isLevelingUp && 'animate-bounce'
              )}
            >
              {level}
            </div>
            <span className="text-sm text-muted-foreground">Level {level}</span>
          </div>
          <span className="text-sm text-muted-foreground">
            {currentXP}/{requiredXP} XP
          </span>
        </div>
        
        {/* Mobile view - compact */}
        <div className="flex sm:hidden items-center gap-2">
          <div
            className={cn(
              'flex items-center justify-center w-6 h-6 rounded-full bg-accent text-primary-foreground font-bold text-xs',
              isLevelingUp && 'animate-bounce'
            )}
          >
            {level}
          </div>
          <div className="flex-1">
            <Progress
              value={progress}
              className={cn(
                'h-1.5 transition-all duration-500',
                isLevelingUp && 'animate-pulse'
              )}
            />
          </div>
          <span className="text-xs text-muted-foreground">
            {currentXP}/{requiredXP}
          </span>
        </div>
        
        {/* Progress bar for desktop/tablet */}
        <div className="hidden sm:block">
          <Progress
            value={progress}
            className={cn(
              'h-2 transition-all duration-500',
              isLevelingUp && 'animate-pulse'
            )}
          />
        </div>
      </div>
    </div>
  );
};
