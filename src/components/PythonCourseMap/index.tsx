'use client';

import { useState } from 'react';
import { Doc } from '@/convex/_generated/dataModel';
import { Map } from 'lucide-react';

import { Button } from '@/shadcn/button';
import { Dialog, DialogTrigger } from '@/shadcn/dialog';

import type { Chapter } from '../PythonMapPopup';
import { PythonMapPopup } from '../PythonMapPopup';

interface PythonCourseMapProps {
  chapters?: Doc<'chapters'>[];
  lessons?: Doc<'lessons'>[];
  progress?: { lesson_id: string; is_completed: boolean }[];
}

export const PythonCourseMap = ({
  chapters = [],
  lessons = [],
  progress = [],
}: PythonCourseMapProps) => {
  const [open, setOpen] = useState(false);
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);

  // Transform chapters from DB into the format required by PythonMapPopup
  const mapChaptersToRoadmap = (): Chapter[] => {
    if (!chapters.length) return defaultChapters;

    // Arrange chapters in a zigzag pattern
    return chapters.map((chapter, index) => {
      // Calculate position based on zigzag pattern
      // Odd rows go left to right, even rows go right to left
      const row = Math.floor(index / 2);
      const isEvenRow = row % 2 === 0;

      // For even rows (0, 2, 4...), first node is left, second is right
      // For odd rows (1, 3, 5...), first node is right, second is left
      const isFirstInRow = index % 2 === 0;

      // X position depends on whether it's left or right position
      const xPos =
        (isEvenRow && isFirstInRow) || (!isEvenRow && !isFirstInRow)
          ? 200 // Left position
          : 600; // Right position

      // Y position increases with each row
      const yPos = 100 + row * 200;

      // Count lessons in this chapter
      const chapterLessons = lessons.filter(
        lesson => lesson.chapter_id === chapter._id
      );

      // Check which lessons are completed using progress data
      const completedLessonsCount = chapterLessons.filter(lesson =>
        progress.some(p => p.lesson_id === lesson._id && p.is_completed)
      ).length;

      // Determine status based on lessons
      let status: 'completed' | 'available' | 'locked' = 'locked';
      if (index === 0) {
        status = 'available'; // First chapter is always available
      } else if (
        index > 0 &&
        completedLessonsCount === chapterLessons.length &&
        chapterLessons.length > 0
      ) {
        status = 'completed';
      } else if (index > 0) {
        // Check if previous chapter is completed
        const prevIndex = index - 1;
        const prevChapter = chapters[prevIndex];
        const prevChapterLessons = lessons.filter(
          lesson => lesson.chapter_id === prevChapter._id
        );

        const prevCompletedLessonsCount = prevChapterLessons.filter(lesson =>
          progress.some(p => p.lesson_id === lesson._id && p.is_completed)
        ).length;

        if (
          prevCompletedLessonsCount === prevChapterLessons.length &&
          prevChapterLessons.length > 0
        ) {
          status = 'available';
        }
      }

      return {
        id: index + 1,
        title: chapter.name || `Chapter ${index + 1}`,
        status,
        position: { x: xPos, y: yPos },
        xp: 50 + index * 20, // Just a placeholder XP value
        dbId: chapter._id, // Store the database ID
      };
    });
  };

  // Default chapters in case none are provided
  const defaultChapters: Chapter[] = [
    {
      id: 1,
      title: 'Hello World!',
      status: 'completed',
      position: { x: 200, y: 100 },
      xp: 227,
    },
    {
      id: 2,
      title: 'Variables & Data Types',
      status: 'available',
      position: { x: 600, y: 100 },
      xp: 105,
    },
    {
      id: 3,
      title: 'Control Flow',
      status: 'locked',
      position: { x: 600, y: 300 },
      xp: 73,
    },
    {
      id: 4,
      title: 'Functions & Modules',
      status: 'locked',
      position: { x: 200, y: 300 },
      xp: 68,
    },
    {
      id: 5,
      title: 'Data Structures',
      status: 'locked',
      position: { x: 200, y: 500 },
      xp: 95,
    },
    {
      id: 6,
      title: 'Object-Oriented Programming',
      status: 'locked',
      position: { x: 600, y: 500 },
      xp: 120,
    },
  ];

  const roadmapChapters = mapChaptersToRoadmap();

  return (
    <Dialog
      open={open}
      onOpenChange={setOpen}
    >
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 font-mono ml-2"
        >
          <Map className="h-4 w-4" />
          Course Map
        </Button>
      </DialogTrigger>
      <PythonMapPopup
        chapters={roadmapChapters}
        selectedChapter={selectedChapter}
        setSelectedChapter={setSelectedChapter}
        open={open}
        onOpenChange={setOpen}
      />
    </Dialog>
  );
};

export default PythonCourseMap;
