import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export type State = {
  lessonId:
    | {
        _id: string;
        id: number;
      }
    | {
        _id: null;
        id: null;
      };
  setLessonId: (newState: { _id: string; id: number }) => void;
};

export const useCodeStore = create<State>()(
  devtools(
    immer(set => ({
      lessonId: {
        _id: null,
        id: null,
      },
      setLessonId: (newState: { _id: string; id: number }) =>
        set((state: State) => {
          state.lessonId = newState;
        }),
    }))
  )
);
