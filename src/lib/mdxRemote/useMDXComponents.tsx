import Image, { ImageProps } from 'next/image';
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from '@/shadcn/scroll-area';
import { cn } from '@/utils';
import langJavaScript from 'highlight.js/lib/languages/javascript';
import langPython from 'highlight.js/lib/languages/python';
import langTypeScript from 'highlight.js/lib/languages/typescript';
import type { MDXComponents } from 'mdx/types';
import rehypeHighlight from 'rehype-highlight';

// Add more languages as needed
import { MDXCode } from '@/components/MDX/Code';

// This file allows you to provide custom React components
// to be used in MDX files. You can import and use any
// React component you want, including inline styles,
// components from other libraries, and more.

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    h1: ({ children, className }) => (
      <h1 className={cn(className, 'p-2 text-2xl font-bold')}>{children}</h1>
    ),

    h2: ({ children, className }) => (
      <h2 className={cn(className, 'p-2 text-xl font-bold')}>{children}</h2>
    ),

    h3: ({ children, className }) => (
      <h3 className={cn(className, 'p-2 text-l font-bold')}>{children}</h3>
    ),

    p: ({ children, className }) => (
      <p className={cn(className, 'p-2 text-l')}>{children}</p>
    ),

    ul: ({ children, className }) => (
      <ul className={cn(className, 'p-2 list-disc ml-4 inline-block')}>
        {children}
      </ul>
    ),

    code: ({ children, className }) => (
      <MDXCode classname={className}>{children}</MDXCode>
    ),

    pre: ({ children, className }) => (
      <pre
        className={cn(
          'not-prose relative overflow-x-auto border p-2 shadow',
          className
        )}
      >
        <ScrollArea
          className="py-2"
          type="always"
        >
          {children}
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </pre>
    ),

    img: props => (
      <Image
        sizes="100vw"
        style={{ width: '100%', height: 'auto' }}
        alt={props?.alt ?? ''}
        {...(props as Omit<ImageProps, 'alt'>)}
      />
    ),
    ...components,
  };
}

export const mdxOptions = {
  mdxOptions: {
    remarkPlugins: [],
    rehypePlugins: [
      [
        rehypeHighlight,
        {
          languages: {
            python: langPython,
            javascript: langJavaScript,
            typescript: langTypeScript,
          },
        },
      ],
    ],
  },
};
