import { Id } from '@/convex/_generated/dataModel';

export type CardRarity = 'legendary' | 'epic' | 'rare' | 'common';
export type CardVariation =
  | 'holographic'
  | 'golden'
  | 'dark'
  | 'rainbow'
  | 'standard'
  | 'frost'
  | 'metallic';

export interface CardStats {
  power: number;
  difficulty: number;
  popularity: number;
}

export interface CardDetails {
  concepts: string[];
  tips: string[];
  resources: string[];
}

export interface CollectibleCard {
  _id: Id<'collectible_cards'>;
  name: string;
  description: string;
  rarity: CardRarity;
  variation?: CardVariation;
  image_url: string;
  background_image?: string | undefined;
  card_type?: string;
  stats: CardStats;
  details: CardDetails;
}

export interface CollectibleCardWithStatus {
  card: CollectibleCard;
  isCollected: boolean;
}
