'use client';

import { ReactNode } from 'react';
import { Clerk<PERSON><PERSON><PERSON>, useAuth } from '@clerk/nextjs';
import { ConvexReactClient } from 'convex/react';
import { ConvexProviderWithClerk } from 'convex/react-clerk';

// import { scan } from 'react-scan';

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

const ConvexClerkClientProvider = ({ children }: { children: ReactNode }) => {
  // scan({
  //   enabled: true,
  // });

  return (
    <ClerkProvider
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!}
    >
      <ConvexProviderWithClerk
        client={convex}
        useAuth={useAuth}
      >
        {children}
      </ConvexProviderWithClerk>
    </ClerkProvider>
  );
};

export { ConvexClerkClientProvider };
