'use client';

import { useIsClient } from '@uidotdev/usehooks';
import {
  ThemeProvider as NextThemesProvider,
  ThemeProviderProps,
} from 'next-themes';

const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const isClient = useIsClient();

  if (!isClient) {
    return <>{children}</>;
  }

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      disableTransitionOnChange
    >
      {children}
    </NextThemesProvider>
  );
};

export { ThemeProvider };
