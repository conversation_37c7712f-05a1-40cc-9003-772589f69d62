/*
 * Copyright (c) 2017-present Arctic Ice Studio <<EMAIL>>
 * Copyright (c) 2017-present <PERSON> <<EMAIL>>
 *
 * Project:    Nord highlight.js
 * Version:    0.1.0
 * Repository: https://github.com/arcticicestudio/nord-highlightjs
 * License:    MIT
 * References:
 *   https://github.com/arcticicestudio/nord
 */

/*

Polar Night

#2E3440
#3B4252
#434C5E
#4C566A

Snow Storm

#D8DEE9
#E5E9F0
#ECEFF4

Frost

#8FBCBB
#88C0D0
#81A1C1
#5E81AC

Aurora

#BF616A
#D08770
#EBCB8B
#A3BE8C
#B48EAD

*/

html.dark {
  .hljs {
    background: #2e3440;
  }

  .hljs,
  .hljs-subst {
    color: #d8dee9;
  }

  .hljs-selector-tag {
    color: #81a1c1;
  }

  .hljs-selector-id {
    color: #8fbcbb;
    font-weight: bold;
  }

  .hljs-selector-class {
    color: #8fbcbb;
  }

  .hljs-selector-attr {
    color: #8fbcbb;
  }

  .hljs-property {
    color: #88c0d0;
  }

  .hljs-selector-pseudo {
    color: #88c0d0;
  }

  .hljs-addition {
    background-color: rgba(163, 190, 140, 0.5);
  }

  .hljs-deletion {
    background-color: rgba(191, 97, 106, 0.5);
  }

  .hljs-built_in,
  .hljs-type {
    color: #8fbcbb;
  }

  .hljs-class {
    color: #8fbcbb;
  }

  .hljs-function {
    color: #88c0d0;
  }

  .hljs-title.hljs-function,
  .hljs-function > .hljs-title {
    color: #88c0d0;
  }

  .hljs-keyword,
  .hljs-literal,
  .hljs-symbol {
    color: #81a1c1;
  }

  .hljs-number {
    color: #b48ead;
  }

  .hljs-regexp {
    color: #ebcb8b;
  }

  .hljs-string {
    color: #a3be8c;
  }

  .hljs-title {
    color: #8fbcbb;
  }

  .hljs-params {
    color: #d8dee9;
  }

  .hljs-bullet {
    color: #81a1c1;
  }

  .hljs-code {
    color: #8fbcbb;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-formula {
    color: #8fbcbb;
  }

  .hljs-strong {
    font-weight: bold;
  }

  .hljs-link:hover {
    text-decoration: underline;
  }

  .hljs-quote {
    color: #4c566a;
  }

  .hljs-comment {
    color: #4c566a;
  }

  .hljs-doctag {
    color: #8fbcbb;
  }

  .hljs-meta,
  .hljs-meta .hljs-keyword {
    color: #5e81ac;
  }

  .hljs-meta .hljs-string {
    color: #a3be8c;
  }

  .hljs-attr {
    color: #8fbcbb;
  }

  .hljs-attribute {
    color: #d8dee9;
  }

  .hljs-name {
    color: #81a1c1;
  }

  .hljs-section {
    color: #88c0d0;
  }

  .hljs-tag {
    color: #81a1c1;
  }

  .hljs-variable {
    color: #d8dee9;
  }

  .hljs-template-variable {
    color: #d8dee9;
  }

  .hljs-template-tag {
    color: #5e81ac;
  }

  /* per language customizations */

  .language-abnf .hljs-attribute {
    color: #88c0d0;
  }

  .language-abnf .hljs-symbol {
    color: #ebcb8b;
  }

  .language-apache .hljs-attribute {
    color: #88c0d0;
  }

  .language-apache .hljs-section {
    color: #81a1c1;
  }

  .language-arduino .hljs-built_in {
    color: #88c0d0;
  }

  .language-aspectj .hljs-meta {
    color: #d08770;
  }

  .language-aspectj > .hljs-title {
    color: #88c0d0;
  }

  .language-bnf .hljs-attribute {
    color: #8fbcbb;
  }

  .language-clojure .hljs-name {
    color: #88c0d0;
  }

  .language-clojure .hljs-symbol {
    color: #ebcb8b;
  }

  .language-coq .hljs-built_in {
    color: #88c0d0;
  }

  .language-cpp .hljs-meta .hljs-string {
    color: #8fbcbb;
  }

  .language-css .hljs-built_in {
    color: #88c0d0;
  }

  .language-css .hljs-keyword {
    color: #d08770;
  }

  .language-diff .hljs-meta {
    color: #8fbcbb;
  }

  .language-ebnf .hljs-attribute {
    color: #8fbcbb;
  }

  .language-glsl .hljs-built_in {
    color: #88c0d0;
  }

  .language-groovy .hljs-meta:not(:first-child) {
    color: #d08770;
  }

  .language-haxe .hljs-meta {
    color: #d08770;
  }

  .language-java .hljs-meta {
    color: #d08770;
  }

  .language-ldif .hljs-attribute {
    color: #8fbcbb;
  }

  .language-lisp .hljs-name {
    color: #88c0d0;
  }

  .language-lua .hljs-built_in {
    color: #88c0d0;
  }

  .language-moonscript .hljs-built_in {
    color: #88c0d0;
  }

  .language-nginx .hljs-attribute {
    color: #88c0d0;
  }

  .language-nginx .hljs-section {
    color: #5e81ac;
  }

  .language-pf .hljs-built_in {
    color: #88c0d0;
  }

  .language-processing .hljs-built_in {
    color: #88c0d0;
  }

  .language-scss .hljs-keyword {
    color: #81a1c1;
  }

  .language-stylus .hljs-keyword {
    color: #81a1c1;
  }

  .language-swift .hljs-meta {
    color: #d08770;
  }

  .language-vim .hljs-built_in {
    color: #88c0d0;
    font-style: italic;
  }

  .language-yaml .hljs-meta {
    color: #d08770;
  }
}
