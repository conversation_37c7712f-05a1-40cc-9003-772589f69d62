@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));
 
@theme {
  --color-border: var(--color-gruvbox-bg3);
  --color-input: var(--color-gruvbox-bg3);
  --color-ring: var(--color-gruvbox-blue-dark);
  --color-background: var(--color-gruvbox-bg0-soft);
  --color-foreground: var(--color-gruvbox-fg-dark);

  --color-primary: var(--color-gruvbox-blue-dark);
  --color-primary-foreground: var(--color-gruvbox-fg0);

  --color-secondary: var(--color-gruvbox-bg2);
  --color-secondary-foreground: var(--color-gruvbox-fg2);

  --color-destructive: var(--color-gruvbox-red-dark);
  --color-destructive-foreground: var(--color-gruvbox-fg0);

  --color-muted: var(--color-gruvbox-bg2);
  --color-muted-foreground: var(--color-gruvbox-fg3);

  --color-accent: var(--color-gruvbox-yellow-dark);
  --color-accent-foreground: var(--color-gruvbox-bg-dark);

  --color-popover: var(--color-gruvbox-bg1);
  --color-popover-foreground: var(--color-gruvbox-fg-dark);

  --color-card: var(--color-gruvbox-bg1);
  --color-card-foreground: var(--color-gruvbox-fg-dark);

  --color-sidebar: var(--color-gruvbox-bg1);
  --color-sidebar-foreground: var(--color-gruvbox-fg-dark);
  --color-sidebar-primary: var(--color-gruvbox-blue-dark);
  --color-sidebar-primary-foreground: var(--color-gruvbox-fg0);
  --color-sidebar-accent: var(--color-gruvbox-bg2);
  --color-sidebar-accent-foreground: var(--color-gruvbox-fg2);
  --color-sidebar-border: var(--color-gruvbox-bg3);
  --color-sidebar-ring: var(--color-gruvbox-blue-light);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  /* Gruvbox inspired color palette */
  --color-gruvbox-bg-dark: #282828;
  --color-gruvbox-fg-dark: #ebdbb2;
  --color-gruvbox-bg-darker: #1d2021;
  --color-gruvbox-red-dark: #cc241d;
  --color-gruvbox-red-light: #fb4934;
  --color-gruvbox-green-dark: #98971a;
  --color-gruvbox-green-light: #b8bb26;
  --color-gruvbox-yellow-dark: #d79921;
  --color-gruvbox-yellow-light: #fabd2f;
  --color-gruvbox-blue-dark: #458588;
  --color-gruvbox-blue-light: #83a598;
  --color-gruvbox-purple-dark: #b16286;
  --color-gruvbox-purple-light: #d3869b;
  --color-gruvbox-aqua-dark: #689d6a;
  --color-gruvbox-aqua-light: #8ec07c;
  --color-gruvbox-bg-gray: #928374;
  --color-gruvbox-gray-light: #a89984;
  --color-gruvbox-orange-dark: #d65d0e;
  --color-gruvbox-orange-light: #fe8019;
  --color-gruvbox-bg0-soft: #32302f;
  --color-gruvbox-bg1: #3c3836;
  --color-gruvbox-bg2: #504945;
  --color-gruvbox-bg3: #665c54;
  --color-gruvbox-bg4: #7c6f64;
  --color-gruvbox-fg0: #fbf1c7;
  --color-gruvbox-fg2: #d5c4a1;
  --color-gruvbox-fg3: #bdae93;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1600px) {
    max-width: 1600px;
  }
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  :root {
    --gradient: linear-gradient(to top left, var(--color-gruvbox-bg-dark), var(--color-gruvbox-bg-darker));
    --background: var(--color-gruvbox-bg0-soft);
    --foreground: var(--color-gruvbox-fg-dark);

    --muted: var(--color-gruvbox-bg2);
    --muted-foreground: var(--color-gruvbox-fg3);

    --popover: var(--color-gruvbox-bg1);
    --popover-foreground: var(--color-gruvbox-fg-dark);

    --card: var(--color-gruvbox-bg1);
    --card-foreground: var(--color-gruvbox-fg-dark);

    --border: var(--color-gruvbox-bg3);
    --input: var(--color-gruvbox-bg3);

    --primary: var(--color-gruvbox-blue-dark);
    --primary-foreground: var(--color-gruvbox-fg0);

    --secondary: var(--color-gruvbox-bg2);
    --secondary-foreground: var(--color-gruvbox-fg2);

    --accent: var(--color-gruvbox-yellow-dark);
    --accent-foreground: var(--color-gruvbox-bg-dark);

    --destructive: var(--color-gruvbox-red-dark);
    --destructive-foreground: var(--color-gruvbox-fg0);

    --ring: var(--color-gruvbox-blue-dark);

    --radius: 0.5rem;

    /* Sidebar Variables */
    --sidebar-background: var(--color-gruvbox-bg1);
    --sidebar-foreground: var(--color-gruvbox-fg-dark);
    --sidebar-primary: var(--color-gruvbox-blue-dark);
    --sidebar-primary-foreground: var(--color-gruvbox-fg0);
    --sidebar-accent: var(--color-gruvbox-bg2);
    --sidebar-accent-foreground: var(--color-gruvbox-fg2);
    --sidebar-border: var(--color-gruvbox-bg3);
    --sidebar-ring: var(--color-gruvbox-blue-light);
  }

  .dark {
    --background: var(--color-gruvbox-bg-dark);
    --foreground: var(--color-gruvbox-fg-dark);
    --card: var(--color-gruvbox-bg1);
    --card-foreground: var(--color-gruvbox-fg-dark);
    --popover: var(--color-gruvbox-bg-darker);
    --popover-foreground: var(--color-gruvbox-fg-dark);
    --primary: var(--color-gruvbox-green-light);
    --primary-foreground: var(--color-gruvbox-bg-dark);
    --secondary: var(--color-gruvbox-bg2);
    --secondary-foreground: var(--color-gruvbox-fg0);
    --muted: var(--color-gruvbox-bg2);
    --muted-foreground: var(--color-gruvbox-gray-light);
    --accent: var(--color-gruvbox-bg3);
    --accent-foreground: var(--color-gruvbox-fg0);
    --destructive: var(--color-gruvbox-red-light);
    --destructive-foreground: var(--color-gruvbox-fg0);
    --border: var(--color-gruvbox-bg2);
    --input: var(--color-gruvbox-bg2);
    --ring: var(--color-gruvbox-green-dark);

    /* Dark mode sidebar variables */
    --sidebar-background: var(--color-gruvbox-bg-darker);
    --sidebar-foreground: var(--color-gruvbox-fg-dark);
    --sidebar-primary: var(--color-gruvbox-blue-light);
    --sidebar-primary-foreground: var(--color-gruvbox-bg-dark);
    --sidebar-accent: var(--color-gruvbox-bg2);
    --sidebar-accent-foreground: var(--color-gruvbox-fg-dark);
    --sidebar-border: var(--color-gruvbox-bg2);
    --sidebar-ring: var(--color-gruvbox-blue-light);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}


/* Card Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.preserve-3d {
  transform-style: preserve-3d;
}

.perspective-2000 {
  perspective: 2000px;
}

/* Gradient Animation */
@keyframes gradient-xy {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-xy {
  background-size: 400% 400%;
  animation: gradient-xy 15s ease infinite;
}

/* Card Hover Effects */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.card-container:hover {
  animation: float 3s ease-in-out infinite;
}

/* Card Rarity Animations */
@keyframes legendary-shine {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.border-yellow-400 {
  background: linear-gradient(60deg, #ffd700, #ffa500, #ffd700);
  background-size: 200% 200%;
  animation: legendary-shine 3s linear infinite;
}


/* 
1)code blocks horizontal scroll 
2) and text wrap 
enabled 
*/
[data-radix-scroll-area-viewport] > div {
  display: block !important;
}

/* code-mirror solution view full height */
.cm-theme {
  height: 100%;
  width: 100%;
}

.cm-merge-theme,
.cm-mergeView,
.cm-mergeViewEditors,
.cm-mergeViewEditor,
.cm-mergeView .cm-mergeViewEditor .cm-editor,
.cm-mergeView .cm-mergeViewEditor .cm-editor .cm-scroller {
  height: 100% !important;
}
