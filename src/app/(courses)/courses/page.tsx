import AvailableCourses from '@/components/AvailableCourses';

const CoursesPage = async () => (
  <div className="min-h-screen bg-gradient-to-b from-gruvbox-bg-darker via-gruvbox-bg-dark to-gruvbox-bg0-soft">
    <div className="relative w-full max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-8 sm:py-12">
      <div className="absolute inset-0 bg-gradient-to-br from-gruvbox-blue-dark/10 via-gruvbox-purple-dark/5 to-transparent rounded-2xl m-8" />
      <div className="relative bg-gruvbox-bg1/50 backdrop-blur-sm rounded-xl border border-gruvbox-bg3/20 shadow-[0_8px_32px_-8px_rgba(69,133,136,0.15)] p-6">
        <div className="absolute inset-x-0 -top-4 h-1 bg-gradient-to-r from-transparent via-gruvbox-blue-light/20 to-transparent" />
        <AvailableCourses />
      </div>
    </div>
  </div>
);

export default CoursesPage;
