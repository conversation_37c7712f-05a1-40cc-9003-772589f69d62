import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { fetchQuery } from 'convex/nextjs';

import Course from '@/components/Course';

import '@/styling/rehype-themes/github-light.css';

import { LessonParamsProps } from '@/types/general';

const CoursePage = async ({
  params,
}: {
  params: Promise<LessonParamsProps>;
}) => {
  const { course } = await params;

  const courseContent = await fetchQuery(api.courses.getCourse, {
    slug: course,
  });

  const course_id = courseContent?._id;

  if (!course_id) {
    return notFound();
  }

  const [chapters, allLessons] = await Promise.all([
    await fetchQuery(api.chapters.getChapters, { course_id }),
    await fetchQuery(api.lessons.getCourseLessons, { course_id }),
  ]);

  // Filter published lessons for non-admin users
  const publishedLessons = allLessons
    .filter(lesson => lesson.is_published)
    .sort((a, b) => a.order - b.order);

  // Filter out chapters with no published lessons and sort by order
  const chaptersWithLessons = chapters
    .filter(chapter =>
      publishedLessons.some(lesson => lesson.chapter_id === chapter._id)
    )
    .sort((a, b) => a.order - b.order);

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gruvbox-bg-darker via-gruvbox-bg-dark to-gruvbox-bg0-soft">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(69,133,136,0.1),transparent_50%)]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(177,98,134,0.1),transparent_50%)]" />

      <div className="relative w-full h-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="relative overflow-hidden bg-gruvbox-bg1/40 backdrop-blur-md rounded-xl shadow-[0_8px_32px_-8px_rgba(40,40,40,0.3)] border border-gruvbox-bg3/20">
            <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-gruvbox-blue-light/30 to-transparent" />
            <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-gruvbox-purple-light/20 to-transparent" />
            <div className="absolute left-0 inset-y-0 w-px bg-gradient-to-b from-transparent via-gruvbox-blue-light/20 to-transparent" />
            <div className="absolute right-0 inset-y-0 w-px bg-gradient-to-b from-transparent via-gruvbox-purple-light/20 to-transparent" />

            <div className="p-6 sm:p-8">
              <Course
                course={courseContent}
                chapters={chaptersWithLessons}
                lessons={publishedLessons}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoursePage;
