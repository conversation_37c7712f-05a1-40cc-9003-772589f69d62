import { notFound } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { fetchQuery } from 'convex/nextjs';

import Course from '@/components/Course';

import '@/styling/rehype-themes/github-light.css';

import { LessonParamsProps } from '@/types/general';

const CoursePage = async ({
  params,
}: {
  params: Promise<LessonParamsProps>;
}) => {
  const { course } = await params;

  const courseContent = await fetchQuery(api.courses.getCourse, {
    slug: course,
  });

  const course_id = courseContent?._id;

  if (!course_id) {
    return notFound();
  }

  const [chapters, allLessons] = await Promise.all([
    await fetchQuery(api.chapters.getChapters, { course_id }),
    await fetchQuery(api.lessons.getCourseLessons, { course_id }),
  ]);

  // Filter published lessons for non-admin users
  const publishedLessons = allLessons
    .filter(lesson => lesson.is_published)
    .sort((a, b) => a.order - b.order);

  // Filter out chapters with no published lessons and sort by order
  const chaptersWithLessons = chapters
    .filter(chapter =>
      publishedLessons.some(lesson => lesson.chapter_id === chapter._id)
    )
    .sort((a, b) => a.order - b.order);

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-7xl mx-auto">
          <Course
            course={courseContent}
            chapters={chaptersWithLessons}
            lessons={publishedLessons}
          />
        </div>
      </div>
    </div>
  );
};

export default CoursePage;
