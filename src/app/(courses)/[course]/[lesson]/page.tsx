import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import { EditorManager } from '@/components';
import { api } from '@/convex/_generated/api';
import { Doc } from '@/convex/_generated/dataModel';
import { fetchQuery } from 'convex/nextjs';
import { MDXRemote } from 'next-mdx-remote/rsc';
import { isEmpty } from 'radash';

import { LessonParamsProps, SerializeOptions } from '@/types/general';
import { mdxOptions, useMDXComponents } from '@/lib/mdxRemote/useMDXComponents';

const Lesson = async ({ params }: { params: Promise<LessonParamsProps> }) => {
  const { course, lesson } = await params;

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const components = useMDXComponents({});

  let defaultLayout;

  const awaitedCookies = await cookies();

  const mainlayout = awaitedCookies?.get('react-resizable-panels:mainLayout');
  const editorlayout = awaitedCookies?.get(
    'react-resizable-panels:editorLayout'
  );

  if (mainlayout && editorlayout) {
    defaultLayout = {
      mainlayout: JSON.parse(mainlayout.value) as number[] | undefined,
      editorlayout: JSON.parse(editorlayout.value) as number[] | undefined,
    };
  }

  const lessonContent = await fetchQuery(api.lessons.getLessonContent, {
    lesson,
    course,
  });

  const isEmptyData = isEmpty(lessonContent);

  if (isEmptyData) {
    return notFound();
  }

  const mdxComponents = (
    <MDXRemote
      components={components}
      source={lessonContent?.lesson_content ?? ''}
      options={mdxOptions as SerializeOptions}
    />
  );

  return (
    <EditorManager
      lessonContent={lessonContent as Doc<'lessons'>}
      mdxComponents={mdxComponents}
      defaultLayout={defaultLayout}
    />
  );
};

export default Lesson;
