import type { Metadata } from 'next';
import { api } from '@/convex/_generated/api';
import { fetchQuery } from 'convex/nextjs';

export const metadata: Metadata = {
  title: 'Code Playground - Learn to Code by Examples',
  description:
    'Interactive code examples in Python, JavaScript, and other languages. Edit and run code in the browser, see the results without leaving the page, and learn by doing.',
  openGraph: {
    type: 'website',
    url: 'https://code-playground.vercel.app/',
    title: 'Code Playground - Learn to Code by Examples',
    description:
      'Interactive code examples in Python, JavaScript, and other languages. Edit and run code in the browser, see the results without leaving the page, and learn by doing.',
    images: [
      {
        url: 'https://code-playground.vercel.app/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Code Playground - Learn to Code by Examples',
      },
    ],
  },
};

export async function generateStaticParams({
  params: { course },
}: {
  params: { course: string };
}) {
  const courseContent = await fetchQuery(api.courses.getCourse, {
    slug: course,
  });

  const lessons = await fetchQuery(api.lessons.getCourseLessons, {
    // @ts-expect-error
    course_id: courseContent?._id ?? '',
  });

  return lessons.map(lesson => ({
    lesson: lesson.slug,
  }));
}

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <div className="h-screen w-full overflow-hidden">{children}</div>;
}
