import { Header, Toaster } from '@/components';
import { ConvexClerkClientProvider } from '@/providers/convex-clerk-provider';
import { ThemeProvider } from '@/providers/theme-provider';

import { Breadcrumbs } from '@/components/Breadcrumbs';

import '@/styling/globals.css';

import type { Metadata } from 'next';
import { Fira_Code } from 'next/font/google';
import { cn } from '@/utils';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import NextTopLoader from 'nextjs-toploader';

const font = Fira_Code({
  weight: ['400', '700'],
  subsets: ['latin-ext'],
  display: 'swap',
  preload: true,
  variable: '--font-fira-code',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const env = process.env.NODE_ENV;

  return (
    <html
      lang="en"
      className={font.variable}
    >
      {/* <head>
        <Script
          src="https://unpkg.com/react-scan/dist/install-hook.global.js"
          strategy="beforeInteractive"
        />
      </head> */}
      <body
        data-new-gr-c-s-check-loaded="8.929.0"
        data-gr-ext-installed=""
        className={cn(
          font.className,
          'h-screen bg-background text-foreground '
        )}
      >
        {/* <Monitoring
          apiKey="FTYWedJ3mINIZFLoyYEOgCQM4NnqxsZE" // Safe to expose publically
          url="https://monitoring.react-scan.com/api/v1/ingest"
          commit={process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA} // optional but recommended
          branch={process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF} // optional but recommended
        /> */}

        {env === 'production' && (
          <>
            <SpeedInsights />
            <Analytics />
          </>
        )}

        <NextTopLoader
          color="#907d52"
          crawlSpeed={100}
          height={3}
          showSpinner={false}
          easing="ease"
          speed={100}
          shadow="0 0 10px #907d52,0 0 5px #22907d5299DD"
          template='<div class="bar" role="bar"><div class="peg"></div></div>'
        />

        <Toaster />

        <ConvexClerkClientProvider>
          <ThemeProvider>
            <main className="h-screen">
              <Header />

              <div className="h-screen">
                <div className="container mx-auto px-4">
                  <Breadcrumbs />
                  {children}
                </div>
              </div>
            </main>
          </ThemeProvider>
        </ConvexClerkClientProvider>
      </body>
    </html>
  );
}

export const metadata: Metadata = {
  title: 'Code playground',
  description: 'Generated by create next app',
};
