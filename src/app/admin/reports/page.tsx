import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components';
import { api } from '@/convex/_generated/api';
import { fetchQuery } from 'convex/nextjs';

const Reports = async () => {
  const reports = await fetchQuery(api.reports.getReports);

  return (
    <div className="p-2">
      {reports?.length ? (
        <Table>
          <TableCaption>Reports</TableCaption>

          <TableHeader>
            <TableRow>
              <TableHead>Content</TableHead>
              <TableHead>User_ID</TableHead>
              <TableHead>Lesson_ID</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Date</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {reports.map(entry => (
              <TableRow key={entry._id}>
                <TableCell>{entry.content}</TableCell>
                <TableCell>{entry.user_id}</TableCell>
                <TableCell>{entry.lesson_id}</TableCell>
                <TableCell>{entry.email}</TableCell>
                <TableCell>
                  {new Date(entry._creationTime).toLocaleString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <p>No reports found</p>
      )}
    </div>
  );
};

export default Reports;
