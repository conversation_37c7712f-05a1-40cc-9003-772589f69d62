'use client';

import Link from 'next/link';
import { Book<PERSON><PERSON>, FileText, Settings } from 'lucide-react';

import { NextJsUrlType } from '@/types/general';
import { Card, CardContent, CardHeader, CardTitle } from '@/shadcn/card';

export default function AdminDashboardPage() {
  const adminRoutes = [
    {
      title: 'Course settings',
      description: 'Manage course settings and configurations',
      icon: <Settings className="h-5 w-5" />,
      href: '/admin/settings',
    },
    {
      title: 'Add lesson',
      description: 'Add lessons',
      icon: <BookOpen className="h-5 w-5" />,
      href: '/admin/add/lesson',
    },
    {
      title: 'Reports',
      description: 'View and manage user reports and feedback',
      icon: <FileText className="h-5 w-5" />,
      href: '/admin/reports',
    },
    {
      title: 'Global settings',
      description: 'Configure global application settings',
      icon: <Settings className="h-5 w-5" />,
      href: '/settings',
    },
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {adminRoutes.map(route => (
          <Link
            key={route.href}
            href={route.href as NextJsUrlType}
            className="block"
          >
            <Card className="h-full transition-all duration-200 hover:shadow-lg hover:border-primary dark:hover:border-primary dark:hover:bg-gray-800/50">
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2 mb-1">
                  {route.icon}
                  <CardTitle className="text-xl">{route.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{route.description}</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
