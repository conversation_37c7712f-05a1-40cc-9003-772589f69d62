'use client';

import { ChangeEvent, useState } from 'react';
import Link from 'next/link';
import { api } from '@/convex/_generated/api';
import { Doc, Id } from '@/convex/_generated/dataModel';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useMutation, useQuery } from 'convex/react';
import { Edit, Eye } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/shadcn/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shadcn/card';
import { Input } from '@/shadcn/input';
import CourseItem from '@/components/AdminSettings/CourseItem';
import { SortableChapter } from '@/components/AdminSettings/SortableChapter';

export default function AdminSettings() {
  const courses = useQuery(api.courses.getCourses);
  const [selectedCourse, setSelectedCourse] = useState<Doc<'courses'> | null>(
    null
  );
  const [newChapterName, setNewChapterName] = useState('');

  const deleteChapter = useMutation(api.chapters.deleteChapter);
  const deleteLesson = useMutation(api.lessons.deleteLesson);
  const createChapter = useMutation(api.chapters.createChapter);

  const chapters = useQuery(
    api.chapters.getChapters,
    selectedCourse ? { course_id: selectedCourse._id } : 'skip'
  );

  const lessons = useQuery(
    api.lessons.getCourseLessons,
    selectedCourse ? { course_id: selectedCourse._id } : 'skip'
  );

  const updateChapterOrder = useMutation(api.chapters.updateChapterOrder);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleAddChapter = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCourse || !newChapterName.trim()) {
      toast.error('Please select a course and enter a chapter name');
      return;
    }

    try {
      await createChapter({
        name: newChapterName.trim(),
        course_id: selectedCourse._id,
        order: chapters?.length ?? 0,
      });
      setNewChapterName('');
      toast.success('Chapter added successfully');
    } catch (error) {
      console.error('Failed to create chapter:', error);
      toast.error('Failed to create chapter');
    }
  };

  const handleDeleteChapter = async (chapterId: Id<'chapters'>) => {
    try {
      await deleteChapter({ chapter_id: chapterId });
      toast.success('Chapter deleted successfully');
    } catch (error) {
      console.error('Failed to delete chapter:', error);
      toast.error('Failed to delete chapter');
    }
  };

  const handleDeleteLesson = async (lessonId: Id<'lessons'>) => {
    try {
      await deleteLesson({ lesson_id: lessonId });
      toast.success('Lesson deleted successfully');
    } catch (error) {
      console.error('Failed to delete lesson:', error);
      toast.error('Failed to delete lesson');
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    if (
      active.data.current?.type === 'chapter' &&
      over.data.current?.type === 'chapter' &&
      chapters
    ) {
      const oldIndex = chapters.findIndex(ch => ch._id === active.id);
      const newIndex = chapters.findIndex(ch => ch._id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const reorderedChapters = arrayMove(chapters, oldIndex, newIndex);

        try {
          await updateChapterOrder({
            chapters: reorderedChapters.map((chapter, index) => ({
              _id: chapter._id,
              order: index,
            })),
          });
        } catch (error) {
          console.error('Failed to update chapter order:', error);
        }
      }
    }
  };

  const sortedChapters = chapters?.sort((a, b) => a.order - b.order);
  const sortedLessons = lessons?.sort((a, b) => a.order - b.order);

  const renderLessonActions = (lesson: Doc<'lessons'>) => (
    <div className="flex">
      <Link href={`/admin/edit/lesson/${selectedCourse?.slug}/${lesson.slug}`}>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-primary hover:text-primary hover:bg-primary/10"
          title="Edit lesson"
        >
          <Edit className="h-4 w-4" />
        </Button>
      </Link>
      <Link href={`/${lesson.slug}/preview`}>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-blue-500 hover:text-blue-600 hover:bg-blue-100"
          title="Preview lesson"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </Link>
    </div>
  );

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Course Settings</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Courses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {courses?.map(course => (
                <CourseItem
                  key={course._id}
                  course={course}
                  isSelected={course._id === selectedCourse?._id}
                  onClick={() => setSelectedCourse(course)}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {selectedCourse && (
          <Card>
            <CardHeader>
              <CardTitle>Add New Chapter</CardTitle>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={handleAddChapter}
                className="flex gap-2"
              >
                <Input
                  placeholder="Enter chapter name"
                  value={newChapterName}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setNewChapterName(e.target.value)
                  }
                  className="flex-1"
                />

                <Button type="submit">Add Chapter</Button>
              </form>
            </CardContent>
          </Card>
        )}

        {selectedCourse && sortedChapters && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Chapters & Lessons</CardTitle>
            </CardHeader>
            <CardContent>
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={sortedChapters.map(ch => ch._id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-4">
                    {sortedChapters.map((chapter, index) => (
                      <SortableChapter
                        key={chapter._id}
                        chapter={chapter}
                        lessons={sortedLessons?.filter(
                          lesson => lesson.chapter_id === chapter._id
                        )}
                        courseChapters={sortedChapters}
                        index={index}
                        onDeleteChapter={() => handleDeleteChapter(chapter._id)}
                        onDeleteLesson={handleDeleteLesson}
                        renderLessonActions={renderLessonActions}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
