'use client';

import { api } from '@/convex/_generated/api';
import { useUser } from '@clerk/nextjs';
import { useQuery } from 'convex/react';
import { motion } from 'framer-motion';
import {
  Award,
  Calendar,
  CheckCircle2,
  Crown,
  Gem,
  Lock,
  Shield,
  Star,
  Target,
  Trophy,
  Zap,
} from 'lucide-react';

interface Achievement {
  _id: string;
  name: string;
  description: string;
  icon: string;
  points: number;
  requirement_type: string;
  requirement_value: number;
  badge_image: string;
  unlocked_at?: number;
  user_achievement_id?: string;
}

export default function AchievementsPage() {
  const { user } = useUser();
  const achievements = useQuery(
    api.achievements.getAchievements
  ) as Achievement[];
  const userAchievements = useQuery(
    api.achievements.getUserAchievementsWithDetails,
    user?.id ? { user_id: user.id } : 'skip'
  ) as Achievement[];
  const userAchievementIds = useQuery(
    api.achievements.getUserAchievements,
    user?.id ? { user_id: user.id } : 'skip'
  ) as string[];

  // Helper function to get achievement icon
  const getAchievementIcon = (iconName: string, isUnlocked: boolean) => {
    const iconProps = {
      className: `h-6 w-6 ${isUnlocked ? 'text-gruvbox-green-dark' : 'text-gruvbox-bg-gray'}`,
    };

    switch (iconName.toLowerCase()) {
      case 'trophy':
        return <Trophy {...iconProps} />;

      case 'star':
        return <Star {...iconProps} />;

      case 'target':
        return <Target {...iconProps} />;

      case 'zap':
        return <Zap {...iconProps} />;

      case 'award':
        return <Award {...iconProps} />;

      case 'crown':
        return <Crown {...iconProps} />;

      case 'shield':
        return <Shield {...iconProps} />;

      case 'gem':
        return <Gem {...iconProps} />;

      default:
        return <Trophy {...iconProps} />;
    }
  };

  // Helper function to format unlock date
  const formatUnlockDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get user's achievement progress
  const getUserProgress = (achievement: Achievement) => {
    // This would be calculated based on actual user progress
    // For now, return 0 for locked achievements
    return 0;
  };

  // Group achievements by category
  const groupedAchievements =
    achievements?.reduce(
      (acc, achievement) => {
        const category = getAchievementCategory(achievement.requirement_type);
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(achievement);
        return acc;
      },
      {} as Record<string, Achievement[]>
    ) ?? {};

  // Get unlocked achievements count
  const unlockedCount = userAchievementIds?.length || 0;
  const totalCount = achievements?.length || 0;

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-12">
            <h1 className="text-4xl font-mono font-bold mb-3 text-white">
              Achievements
            </h1>
            <p className="text-lg font-mono text-gruvbox-bg-gray">
              Master Python and SQL to unlock special achievements and showcase
              your expertise!
            </p>
          </div>

          {/* Stats Overview */}
          <div className="grid gap-6 md:grid-cols-3 mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-mono text-white">Total Progress</h3>
                <div className="p-2 rounded bg-gruvbox-green-dark/10">
                  <Trophy className="h-5 w-5 text-gruvbox-green-dark" />
                </div>
              </div>
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {unlockedCount}/{totalCount}
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                achievements unlocked
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-mono text-white">
                  Completion Rate
                </h3>
                <div className="p-2 rounded bg-gruvbox-green-dark/10">
                  <Target className="h-5 w-5 text-gruvbox-green-dark" />
                </div>
              </div>
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {totalCount > 0
                  ? Math.round((unlockedCount / totalCount) * 100)
                  : 0}
                %
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                completion rate
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gruvbox-bg-darker p-6 rounded-md border border-gruvbox-fg-dark"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-mono text-white">Total Points</h3>
                <div className="p-2 rounded bg-gruvbox-green-dark/10">
                  <Star className="h-5 w-5 text-gruvbox-green-dark" />
                </div>
              </div>
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {userAchievements?.reduce(
                  (sum, achievement) => sum + (achievement.points || 0),
                  0
                ) || 0}
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                points earned
              </p>
            </motion.div>
          </div>

          {Object.entries(groupedAchievements).map(
            ([category, categoryAchievements]) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-12"
              >
                <h2 className="text-2xl font-mono font-bold mb-6 text-white flex items-center gap-3">
                  <div className="h-1 w-8 bg-gruvbox-green-dark rounded"></div>
                  {category}
                  <div className="h-1 flex-1 bg-gruvbox-fg-dark/20 rounded"></div>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryAchievements.map((achievement, index) => {
                    const isUnlocked = userAchievementIds?.includes(
                      achievement._id
                    );
                    const unlockedAchievement = userAchievements?.find(
                      ua => ua._id === achievement._id
                    );
                    const progress = getUserProgress(achievement);

                    return (
                      <motion.div
                        key={achievement._id}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          delay: 0.5 + index * 0.1,
                          type: 'spring',
                          stiffness: 100,
                          damping: 15,
                        }}
                        whileHover={{
                          y: -5,
                          transition: {
                            type: 'spring',
                            stiffness: 400,
                            damping: 25,
                          },
                        }}
                        className="group"
                      >
                        <div
                          className={`
                            relative bg-gruvbox-bg-darker rounded-lg p-6 border transition-all duration-300
                            ${
                              isUnlocked
                                ? 'border-gruvbox-green-dark/50 shadow-lg shadow-gruvbox-green-dark/10'
                                : 'border-gruvbox-fg-dark hover:border-gruvbox-fg-dark/60'
                            }
                            group-hover:shadow-xl group-hover:shadow-black/20
                          `}
                        >
                          {/* Status indicator */}
                          <div className="absolute top-4 right-4">
                            {isUnlocked ? (
                              <div className="flex items-center gap-1 bg-gruvbox-green-dark/20 text-gruvbox-green-dark text-xs font-mono font-bold px-2 py-1 rounded-full border border-gruvbox-green-dark/30">
                                <CheckCircle2 className="h-3 w-3" />
                                Unlocked
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 bg-gruvbox-bg-gray/20 text-gruvbox-bg-gray text-xs font-mono font-bold px-2 py-1 rounded-full border border-gruvbox-bg-gray/30">
                                <Lock className="h-3 w-3" />
                                Locked
                              </div>
                            )}
                          </div>

                          {/* Points badge */}
                          <div className="absolute top-4 left-4">
                            <div
                              className={`
                              text-xs font-mono font-bold px-2.5 py-1.5 rounded-full border
                              ${
                                isUnlocked
                                  ? 'bg-gruvbox-green-dark/10 text-gruvbox-green-dark border-gruvbox-green-dark/30'
                                  : 'bg-gruvbox-bg-gray/10 text-gruvbox-bg-gray border-gruvbox-bg-gray/30'
                              }
                            `}
                            >
                              {achievement.points} pts
                            </div>
                          </div>

                          {/* Icon and title */}
                          <div className="mt-12 mb-4">
                            <div
                              className={`
                              inline-flex p-3 rounded-lg mb-4 transition-all duration-300
                              ${
                                isUnlocked
                                  ? 'bg-gruvbox-green-dark/10 group-hover:bg-gruvbox-green-dark/20'
                                  : 'bg-gruvbox-bg-gray/10 group-hover:bg-gruvbox-bg-gray/20'
                              }
                            `}
                            >
                              {getAchievementIcon(achievement.icon, isUnlocked)}
                            </div>
                            <h3
                              className={`
                              text-xl font-mono font-bold mb-2 transition-colors duration-300
                              ${
                                isUnlocked
                                  ? 'text-white group-hover:text-gruvbox-green-dark'
                                  : 'text-gruvbox-bg-gray'
                              }
                            `}
                            >
                              {achievement.name}
                            </h3>
                          </div>

                          {/* Description */}
                          <p
                            className={`
                            font-mono text-sm mb-6 leading-relaxed
                            ${
                              isUnlocked
                                ? 'text-gruvbox-bg-gray'
                                : 'text-gruvbox-bg-gray/70'
                            }
                          `}
                          >
                            {achievement.description}
                          </p>

                          {/* Progress or unlock date */}
                          <div className="space-y-3">
                            {isUnlocked && unlockedAchievement?.unlocked_at ? (
                              <div className="flex items-center gap-2 text-sm font-mono text-gruvbox-green-dark">
                                <Calendar className="h-4 w-4" />

                                <span>
                                  Unlocked on{' '}
                                  {formatUnlockDate(
                                    unlockedAchievement.unlocked_at
                                  )}
                                </span>
                              </div>
                            ) : (
                              <div className="space-y-2">
                                <div className="flex items-center justify-between text-sm font-mono text-gruvbox-bg-gray">
                                  <span>Progress</span>
                                  <span>
                                    {progress}/{achievement.requirement_value}
                                  </span>
                                </div>
                                <div className="w-full bg-gruvbox-bg-gray/20 rounded-full h-2">
                                  <div
                                    className="bg-gruvbox-green-dark/50 h-2 rounded-full transition-all duration-500"
                                    style={{
                                      width: `${Math.min((progress / achievement.requirement_value) * 100, 100)}%`,
                                    }}
                                  ></div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            )
          )}
        </div>
      </div>
    </div>
  );
}

function getAchievementCategory(requirementType: string): string {
  if (requirementType.includes('sql')) {
    return 'SQL Mastery';
  } else if (requirementType.includes('python')) {
    return 'Python Excellence';
  } else if (
    requirementType.includes('performance') ||
    requirementType.includes('optimization')
  ) {
    return 'Performance & Optimization';
  } else if (requirementType.includes('security')) {
    return 'Security & Best Practices';
  } else {
    return 'General Skills';
  }
}
