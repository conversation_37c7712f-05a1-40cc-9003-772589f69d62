'use client';

import { api } from '@/convex/_generated/api';
import { useQuery } from 'convex/react';
import { motion } from 'framer-motion';

interface Achievement {
  _id: string;
  name: string;
  description: string;
  icon: string;
  points: number;
  requirement_type: string;
  requirement_value: number;
  badge_image: string;
}

export default function AchievementsPage() {
  const achievements = useQuery(
    api.achievements.getAchievements
  ) as Achievement[];
  const userAchievements = useQuery(api.achievements.getUserAchievements, {
    user_id: 'placeholder',
  }) as string[];

  // Group achievements by category
  const groupedAchievements =
    achievements?.reduce(
      (acc, achievement) => {
        const category = getAchievementCategory(achievement.requirement_type);
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(achievement);
        return acc;
      },
      {} as Record<string, Achievement[]>
    ) ?? {};

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-12">
            <h1 className="text-4xl font-mono font-bold mb-3 text-white">
              Achievements
            </h1>
            <p className="text-lg font-mono text-gruvbox-bg-gray">
              Master Python and SQL to unlock special achievements and showcase
              your expertise!
            </p>
          </div>

          {Object.entries(groupedAchievements).map(
            ([category, categoryAchievements]) => (
              <div
                key={category}
                className="mb-12"
              >
                <h2 className="text-2xl font-mono font-bold mb-6 text-white">
                  {category}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryAchievements.map((achievement, index) => (
                    <motion.div
                      key={achievement._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div
                        className={`
                      bg-gruvbox-bg-darker border border-gruvbox-bg-darker rounded-md p-6
                      ${
                        userAchievements?.includes(achievement._id)
                          ? 'border-gruvbox-green-dark/50'
                          : 'opacity-75 hover:opacity-100'
                      }
                    `}
                      >
                        <div className="absolute top-0 right-0 mt-6 mr-6">
                          <div className="bg-gruvbox-green-dark/10 text-gruvbox-green-dark text-xs font-mono font-bold px-2.5 py-1.5 rounded-full">
                            {achievement.points} pts
                          </div>
                        </div>

                        <div className="flex items-center gap-3 mb-4">
                          <div className="p-2.5 rounded bg-gruvbox-green-dark/10">
                            <span className="text-2xl">{achievement.icon}</span>
                          </div>
                          <h3 className="text-xl font-mono font-bold text-white">
                            {achievement.name}
                          </h3>
                        </div>

                        <p className="font-mono text-gruvbox-bg-gray mb-4">
                          {achievement.description}
                        </p>

                        <div className="flex items-center gap-2 text-sm font-mono">
                          {userAchievements?.includes(achievement._id) ? (
                            <div className="flex items-center text-gruvbox-green-dark">
                              <span className="mr-1">✓</span> Unlocked
                            </div>
                          ) : (
                            <div className="flex items-center text-gruvbox-bg-gray">
                              <span className="mr-1">🔒</span>
                              Progress: 0/{achievement.requirement_value}
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
}

function getAchievementCategory(requirementType: string): string {
  if (requirementType.includes('sql')) {
    return 'SQL Mastery';
  } else if (requirementType.includes('python')) {
    return 'Python Excellence';
  } else if (
    requirementType.includes('performance') ||
    requirementType.includes('optimization')
  ) {
    return 'Performance & Optimization';
  } else if (requirementType.includes('security')) {
    return 'Security & Best Practices';
  } else {
    return 'General Skills';
  }
}
