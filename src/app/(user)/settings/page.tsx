import { Settings as SettingsIcon } from 'lucide-react';

const Settings = () => {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-2 mb-8">
        <SettingsIcon className="h-8 w-8 text-gruvbox-green-dark" />

        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <div className="grid gap-6">
        <section className="rounded-lg border border-gruvbox-fg-dark p-6">
          <h2 className="text-xl font-semibold mb-4">Account Settings</h2>
          <p className="text-gruvbox-bg-gray">
            Manage your account preferences and personal information.
          </p>
        </section>

        <section className="rounded-lg border border-gruvbox-fg-dark p-6">
          <h2 className="text-xl font-semibold mb-4">Appearance</h2>
          <p className="text-gruvbox-bg-gray">
            Customize the look and feel of your learning environment.
          </p>
        </section>

        <section className="rounded-lg border border-gruvbox-fg-dark p-6">
          <h2 className="text-xl font-semibold mb-4">Notifications</h2>
          <p className="text-gruvbox-bg-gray">
            Configure how and when you receive notifications.
          </p>
        </section>
      </div>
    </div>
  );
};

export default Settings;
