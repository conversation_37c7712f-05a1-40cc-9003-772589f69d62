'use client';

import { useState } from 'react';
import { Plus, Save, Trash2, Edit3, Map<PERSON><PERSON>, Arrow<PERSON>ight, Undo, <PERSON>o } from 'lucide-react';

import { <PERSON><PERSON> } from '@/shadcn/button';
import { Input } from '@/shadcn/input';
import { Textarea } from '@/shadcn/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/shadcn/card';
import { Badge } from '@/shadcn/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/shadcn/dialog';
import { Label } from '@/shadcn/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shadcn/select';

interface RoadmapPoint {
  id: string;
  x: number;
  y: number;
  title: string;
  description: string;
  skills: string[];
  xp: number;
  status: 'completed' | 'available' | 'locked';
}

interface RoadmapPath {
  id: string;
  name: string;
  description: string;
  points: RoadmapPoint[];
}

const defaultPaths: RoadmapPath[] = [
  {
    id: 'python-basics',
    name: 'Python Fundamentals',
    description: 'Master the basics of Python programming',
    points: [
      {
        id: '1',
        x: 10,
        y: 15,
        title: 'Hello World!',
        description: 'Start your Python journey with basic syntax and your first program',
        skills: ['Print statements', 'Basic syntax', 'Running Python code'],
        xp: 50,
        status: 'available'
      },
      {
        id: '2',
        x: 25,
        y: 25,
        title: 'Variables & Data Types',
        description: 'Learn to store and manipulate different types of data',
        skills: ['Variables', 'Strings', 'Numbers', 'Booleans'],
        xp: 75,
        status: 'locked'
      },
      {
        id: '3',
        x: 45,
        y: 20,
        title: 'Control Flow',
        description: 'Master decision making and loops in your programs',
        skills: ['If statements', 'For loops', 'While loops', 'Conditions'],
        xp: 100,
        status: 'locked'
      }
    ]
  }
];

export default function LearningPathsPage() {
  const [paths, setPaths] = useState<RoadmapPath[]>(defaultPaths);
  const [selectedPath, setSelectedPath] = useState<RoadmapPath | null>(paths[0]);
  const [selectedPoint, setSelectedPoint] = useState<RoadmapPoint | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingPoint, setEditingPoint] = useState<RoadmapPoint | null>(null);
  const [isAddingPoint, setIsAddingPoint] = useState(false);
  const [newPointPosition, setNewPointPosition] = useState<{ x: number; y: number } | null>(null);

  // Handle canvas click to add new point
  const handleCanvasClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!isAddingPoint || !selectedPath) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;
    
    setNewPointPosition({ x, y });
    setEditingPoint({
      id: Date.now().toString(),
      x,
      y,
      title: '',
      description: '',
      skills: [],
      xp: 50,
      status: 'locked'
    });
    setIsEditing(true);
  };

  // Save point (new or edited)
  const savePoint = (point: RoadmapPoint) => {
    if (!selectedPath) return;
    
    const updatedPath = {
      ...selectedPath,
      points: selectedPoint
        ? selectedPath.points.map(p => p.id === selectedPoint.id ? point : p)
        : [...selectedPath.points, point]
    };
    
    const updatedPaths = paths.map(p => p.id === selectedPath.id ? updatedPath : p);
    setPaths(updatedPaths);
    setSelectedPath(updatedPath);
    setIsEditing(false);
    setEditingPoint(null);
    setSelectedPoint(null);
    setIsAddingPoint(false);
    setNewPointPosition(null);
  };

  // Delete point
  const deletePoint = (pointId: string) => {
    if (!selectedPath) return;
    
    const updatedPath = {
      ...selectedPath,
      points: selectedPath.points.filter(p => p.id !== pointId)
    };
    
    const updatedPaths = paths.map(p => p.id === selectedPath.id ? updatedPath : p);
    setPaths(updatedPaths);
    setSelectedPath(updatedPath);
    setSelectedPoint(null);
  };

  // Create SVG path from points
  const createPath = (points: RoadmapPoint[]) => {
    if (points.length < 2) return '';
    
    let path = `M ${points[0].x} ${points[0].y}`;
    for (let i = 1; i < points.length; i++) {
      const point = points[i];
      const prevPoint = points[i - 1];
      const controlX = (prevPoint.x + point.x) / 2;
      const controlY = prevPoint.y;
      path += ` Q ${controlX} ${controlY} ${point.x} ${point.y}`;
    }
    return path;
  };

  // Get point style based on status
  const getPointStyle = (status: RoadmapPoint['status'], isSelected: boolean) => {
    if (isSelected) {
      return 'bg-gradient-to-br from-purple-500 to-purple-700 border-purple-300 text-white';
    }
    
    switch (status) {
      case 'completed':
        return 'bg-gradient-to-br from-emerald-500 to-emerald-700 border-emerald-300 text-white';
      case 'available':
        return 'bg-gradient-to-br from-blue-500 to-blue-700 border-blue-300 text-white';
      case 'locked':
        return 'bg-gradient-to-br from-slate-600 to-slate-800 border-slate-400 text-slate-300';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-amber-400 bg-clip-text text-transparent flex items-center gap-3">
            <MapPin className="h-8 w-8 text-blue-400" />
            Learning Path Editor
          </h1>
          <p className="text-slate-400 mt-2">Create and customize interactive learning roadmaps</p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={isAddingPoint ? 'default' : 'outline'}
            onClick={() => {
              setIsAddingPoint(!isAddingPoint);
              if (isAddingPoint) {
                setNewPointPosition(null);
                setEditingPoint(null);
                setIsEditing(false);
              }
            }}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {isAddingPoint ? 'Cancel Adding' : 'Add Point'}
          </Button>
          
          <Button variant="outline" className="flex items-center gap-2">
            <Save className="h-4 w-4" />
            Save Path
          </Button>
        </div>
      </div>

      {/* Path Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Select Learning Path</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {paths.map((path) => (
              <div
                key={path.id}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedPath?.id === path.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                    : 'border-slate-200 dark:border-slate-700 hover:border-slate-300'
                }`}
                onClick={() => setSelectedPath(path)}
              >
                <h3 className="font-semibold">{path.name}</h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{path.description}</p>
                <Badge variant="secondary" className="mt-2">
                  {path.points.length} points
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Editor */}
      {selectedPath && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Roadmap Canvas */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Edit3 className="h-5 w-5" />
                  {selectedPath.name}
                  {isAddingPoint && (
                    <Badge variant="outline" className="ml-2">
                      Click to add point
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="relative w-full h-[600px] border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 overflow-hidden cursor-crosshair"
                  onClick={handleCanvasClick}
                >
                  {/* SVG Path */}
                  {selectedPath.points.length > 1 && (
                    <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 100 100">
                      <path
                        d={createPath(selectedPath.points)}
                        stroke="#3b82f6"
                        strokeWidth="0.5"
                        fill="none"
                        strokeDasharray="2 1"
                        className="opacity-60"
                      />
                    </svg>
                  )}
                  
                  {/* Roadmap Points */}
                  {selectedPath.points.map((point) => (
                    <div
                      key={point.id}
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
                      style={{
                        left: `${point.x}%`,
                        top: `${point.y}%`,
                      }}
                    >
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center border-4 shadow-lg transition-all duration-300 cursor-pointer hover:scale-110 ${
                          getPointStyle(point.status, selectedPoint?.id === point.id)
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedPoint(point);
                        }}
                      >
                        <span className="text-sm font-bold">
                          {selectedPath.points.indexOf(point) + 1}
                        </span>
                      </div>
                      
                      {/* Point tooltip */}
                      <div className="absolute top-14 left-1/2 transform -translate-x-1/2 w-48 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-10">
                        <div className="bg-slate-800 text-white p-3 rounded-lg shadow-xl border border-slate-600">
                          <h4 className="font-semibold text-sm">{point.title}</h4>
                          <p className="text-xs text-slate-300 mt-1">{point.description}</p>
                          <div className="flex items-center justify-between mt-2">
                            <Badge variant="secondary" className="text-xs">
                              {point.xp} XP
                            </Badge>
                            <Badge 
                              variant={point.status === 'completed' ? 'default' : 'outline'}
                              className="text-xs"
                            >
                              {point.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Point Editor Panel */}
          <div className="space-y-4">
            {/* Selected Point Info */}
            {selectedPoint && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Point Details</span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingPoint(selectedPoint);
                          setIsEditing(true);
                        }}
                      >
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => deletePoint(selectedPoint.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold">{selectedPoint.title}</h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      {selectedPoint.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <Badge variant="secondary">{selectedPoint.xp} XP</Badge>
                    <Badge 
                      variant={selectedPoint.status === 'completed' ? 'default' : 'outline'}
                    >
                      {selectedPoint.status}
                    </Badge>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Skills</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedPoint.skills.map((skill, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="text-xs text-slate-500">
                    Position: ({selectedPoint.x.toFixed(1)}, {selectedPoint.y.toFixed(1)})
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Instructions */}
            <Card>
              <CardHeader>
                <CardTitle>Instructions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <p>• Click "Add Point" then click on the canvas to add new points</p>
                <p>• Click on existing points to select and view details</p>
                <p>• Use the edit button to modify point properties</p>
                <p>• Points are automatically connected in order</p>
                <p>• Save your changes when finished</p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Edit Point Dialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedPoint ? 'Edit Point' : 'Add New Point'}
            </DialogTitle>
          </DialogHeader>
          
          {editingPoint && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={editingPoint.title}
                    onChange={(e) => setEditingPoint({ ...editingPoint, title: e.target.value })}
                    placeholder="Enter point title"
                  />
                </div>
                
                <div>
                  <Label htmlFor="xp">XP Reward</Label>
                  <Input
                    id="xp"
                    type="number"
                    value={editingPoint.xp}
                    onChange={(e) => setEditingPoint({ ...editingPoint, xp: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={editingPoint.description}
                  onChange={(e) => setEditingPoint({ ...editingPoint, description: e.target.value })}
                  placeholder="Describe what learners will achieve"
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={editingPoint.status}
                  onValueChange={(value: 'completed' | 'available' | 'locked') => 
                    setEditingPoint({ ...editingPoint, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="locked">Locked</SelectItem>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="skills">Skills (comma-separated)</Label>
                <Input
                  id="skills"
                  value={editingPoint.skills.join(', ')}
                  onChange={(e) => setEditingPoint({ 
                    ...editingPoint, 
                    skills: e.target.value.split(',').map(s => s.trim()).filter(s => s.length > 0)
                  })}
                  placeholder="Enter skills separated by commas"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="x">X Position (%)</Label>
                  <Input
                    id="x"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={editingPoint.x.toFixed(1)}
                    onChange={(e) => setEditingPoint({ ...editingPoint, x: parseFloat(e.target.value) || 0 })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="y">Y Position (%)</Label>
                  <Input
                    id="y"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={editingPoint.y.toFixed(1)}
                    onChange={(e) => setEditingPoint({ ...editingPoint, y: parseFloat(e.target.value) || 0 })}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsEditing(false);
                    setEditingPoint(null);
                    setIsAddingPoint(false);
                    setNewPointPosition(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => savePoint(editingPoint)}
                  disabled={!editingPoint.title.trim()}
                >
                  {selectedPoint ? 'Update Point' : 'Add Point'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}