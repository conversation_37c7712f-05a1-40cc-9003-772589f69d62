'use client';

import { api } from '@/convex/_generated/api';
import { useAuth } from '@clerk/clerk-react';
import { useQuery } from 'convex/react';

import { CollectibleCard } from '@/components/CollectibleCard';

export default function CollectiblesPage() {
  const authObject = useAuth();
  const cards = useQuery(api.cards.getCards);
  const userCards = useQuery(api.user_cards.getUserCards, {
    user_id: authObject?.userId ?? 'placeholder',
  });

  if (!cards || !userCards) {
    return (
      <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
        <div className="container mx-auto p-4">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-4xl font-mono font-bold text-white text-center">
              Loading Your Collection...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  // Create a map of collected cards for efficient lookup
  const collectedCardIds = new Set(userCards.map(uc => uc.card_id));

  // Filter and map cards to include collection status
  const cardsWithCollectionStatus = cards.map(card => ({
    ...card,
    isCollected: collectedCardIds.has(card._id),
  }));

  // Sort cards: collected first, then by rarity and name
  const sortedCards = cardsWithCollectionStatus.sort((a, b) => {
    // First sort by collection status
    if (a.isCollected !== b.isCollected) {
      return a.isCollected ? -1 : 1;
    }

    // Then by rarity (legendary > epic > rare)
    const rarityOrder = { legendary: 3, epic: 2, rare: 1 };
    if (a.rarity !== b.rarity) {
      return (
        rarityOrder[b.rarity as keyof typeof rarityOrder] -
        rarityOrder[a.rarity as keyof typeof rarityOrder]
      );
    }

    // Finally by name
    return a.name.localeCompare(b.name);
  });

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="container mx-auto p-4">
        <div className="max-w-7xl mx-auto">
          <div className="mb-12">
            <h1 className="text-4xl font-mono font-bold mb-3 text-white">
              Your Collectible Cards
            </h1>
            <p className="text-lg font-mono text-gruvbox-bg-gray">
              Discover and collect unique cards as you master SQL concepts
            </p>
          </div>

          <div className="grid grid-cols-3 gap-8 max-w-xl mx-auto mb-12">
            <div className="text-center">
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {userCards.length}
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                Collected
              </p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {cards.length}
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                Total Cards
              </p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-mono font-bold text-gruvbox-green-dark mb-1">
                {Math.round((userCards.length / cards.length) * 100)}%
              </p>
              <p className="text-sm font-mono text-gruvbox-bg-gray">
                Completed
              </p>
            </div>
          </div>

          {userCards.length === 0 ? (
            <div className="bg-gruvbox-bg-darker border border-gruvbox-fg-dark rounded-md p-12 text-center">
              <p className="text-xl font-mono text-white mb-3">
                You haven't collected any cards yet!
              </p>
              <p className="font-mono text-gruvbox-bg-gray">
                Complete lessons and achievements to earn collectible cards.
              </p>
            </div>
          ) : (
            <>
              <p className="text-sm font-mono text-gruvbox-bg-gray text-center mb-8">
                💡 Click on collected cards to flip them and see more details
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {sortedCards.map(card => (
                  <CollectibleCard
                    key={card._id}
                    card={card}
                    isCollected={card.isCollected}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
