import { api } from '@/convex/_generated/api';
import { auth } from '@clerk/nextjs/server';
import { fetchQuery } from 'convex/nextjs';

import { CourseProgressSection } from '@/components/progress/CourseProgressSection';
import { QuickLinks } from '@/components/progress/QuickLinks';
import { StatsOverview } from '@/components/progress/StatsOverview';

const ProgressPage = async () => {
  try {
    const a = await auth();
    const token = (await a?.getToken({ template: 'convex' })) ?? '';
    const userId = a?.userId ?? '';

    const courses = await fetchQuery(api.courses.getCourses, {}, { token });

    // Get courses with content
    const coursesWithContent = await Promise.all(
      courses.map(async course => {
        const chapters = await fetchQuery(
          api.chapters.getChapters,
          { course_id: course._id },
          { token }
        );

        const lessons = await fetchQuery(
          api.lessons.getCourseLessons,
          { course_id: course._id },
          { token }
        );

        return {
          ...course,
          hasContent: chapters.length > 0 && lessons.length > 0,
        };
      })
    );

    // Filter out empty courses
    const nonEmptyCourses = coursesWithContent.filter(
      course => course.hasContent
    );

    const achievements = await fetchQuery(
      api.achievements.getUserAchievements,
      { user_id: userId },
      { token }
    );
    const collectibles = await fetchQuery(
      api.cards.getUserCards,
      { user_id: userId },
      { token }
    );

    const courseProgress = await Promise.all(
      nonEmptyCourses.map(async course => {
        const progress = await fetchQuery(
          api.user_progress.getUserProgress,
          { user_id: userId, course_id: course._id },
          { token }
        );

        const completedLessons = progress.filter(p => p.is_completed).length;
        const lessons = await fetchQuery(
          api.lessons.getCourseLessons,
          { course_id: course._id },
          { token }
        );

        return {
          course,
          completedLessons,
          totalLessons: lessons.length,
          progressPercentage:
            lessons.length > 0
              ? Math.round((completedLessons / lessons.length) * 100)
              : 0,
        };
      })
    );

    const totalAchievements = achievements?.length ?? 0;
    const totalCollectibles = collectibles?.length ?? 0;
    const totalCompletedLessons = courseProgress.reduce(
      (acc, curr) => acc + curr.completedLessons,
      0
    );
    const streakDays = 5; // Replace with actual streak data when available

    return (
      <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
        <div className="container mx-auto p-4">
          <div className="max-w-7xl mx-auto">
            <div className="mb-12">
              <h1 className="text-4xl font-mono font-bold mb-3 text-white">
                Your Learning Progress
              </h1>
              <p className="text-lg font-mono text-gruvbox-bg-gray">
                Track your progress across all courses and achievements
              </p>
            </div>

            <StatsOverview
              totalCompletedLessons={totalCompletedLessons}
              totalAchievements={totalAchievements}
              totalCollectibles={totalCollectibles}
              streakDays={streakDays}
            />

            <CourseProgressSection courseProgress={courseProgress} />

            <QuickLinks />
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error fetching progress:', error);
    return (
      <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark flex items-center justify-center p-4">
        <div className="bg-gruvbox-bg-darker border border-gruvbox-fg-dark rounded-md p-6 max-w-md w-full">
          <h2 className="text-xl font-mono font-bold text-gruvbox-red-dark mb-2">
            Error
          </h2>
          <p className="font-mono text-gruvbox-bg-gray">
            There was an error loading your progress. Please try again later.
          </p>
        </div>
      </div>
    );
  }
};

export default ProgressPage;
