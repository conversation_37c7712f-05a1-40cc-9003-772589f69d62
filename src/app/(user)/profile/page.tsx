'use client';

import Link from 'next/link';
import { useUser } from '@clerk/nextjs';
import { <PERSON>, Settings, Star, Trophy } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/shadcn/avatar';
import { Badge } from '@/shadcn/badge';
import { Button } from '@/shadcn/button';
import { Card } from '@/shadcn/card';

export default function Profile() {
  const { user } = useUser();

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <p className="text-muted-foreground">
          Please sign in to view your profile
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Profile Info Card */}
        <Card className="flex-1 p-6 space-y-4">
          <div className="flex items-center gap-4">
            <Avatar className="w-20 h-20">
              <AvatarImage
                src={user.imageUrl}
                alt={user.fullName || 'Profile'}
              />

              <AvatarFallback>
                {user.firstName?.[0]}
                {user.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-2xl font-bold">{user.fullName}</h1>
              <p className="text-muted-foreground">
                {user.primaryEmailAddress?.emailAddress}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Link href="/settings">
              <Button
                variant="outline"
                size="sm"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </Link>
          </div>
        </Card>

        {/* Stats Card */}
        <Card className="flex-1 p-6">
          <h2 className="text-xl font-semibold mb-4">Your Progress</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-yellow-500" />

              <div>
                <p className="text-sm text-muted-foreground">Achievements</p>
                <p className="text-xl font-semibold">12</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Book className="w-5 h-5 text-blue-500" />

              <div>
                <p className="text-sm text-muted-foreground">Courses</p>
                <p className="text-xl font-semibold">5</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-5 h-5 text-purple-500" />

              <div>
                <p className="text-sm text-muted-foreground">Points</p>
                <p className="text-xl font-semibold">1,250</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Course</Badge>
              <span>Completed "Introduction to TypeScript"</span>
            </div>
            <span className="text-sm text-muted-foreground">2 days ago</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">Achievement</Badge>
              <span>Earned "Quick Learner" badge</span>
            </div>
            <span className="text-sm text-muted-foreground">5 days ago</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
