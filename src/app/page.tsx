import Link from 'next/link';
import { HomePageDemo } from '@/components';
import { auth } from '@clerk/nextjs/server';
import { Bar<PERSON><PERSON>, PlusCircle } from 'lucide-react';

import AvailableCourses from '@/components/AvailableCourses';
import ProtectedAdmin from '@/components/ProtectedAdmin';

export default async function Home() {
  const a = await auth();
  const userId = a?.userId ?? '';

  return (
    <div className="min-h-screen bg-gruvbox-bg-dark dark:bg-gruvbox-bg-dark">
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-8">
        {!userId ? (
          <div className="space-y-12">
            <div
              role="banner"
              className="flex flex-col items-center justify-center min-h-[30vh] text-center mb-4"
            >
              <div className="space-y-6 max-w-3xl">
                <h1 className="text-5xl sm:text-6xl font-mono font-bold tracking-tighter text-white">
                  Welcome to{' '}
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-gruvbox-green-dark via-gruvbox-green-dark to-gruvbox-green-dark/90 drop-shadow-sm">
                    Interactive Learning
                  </span>
                </h1>
                <p className="text-xl sm:text-2xl font-mono text-gruvbox-bg-gray/90">
                  Sign in to start your learning journey with hands-on coding
                  exercises. You can alse try our demo below:
                </p>
              </div>
            </div>

            <div className="max-w-5xl mx-auto">
              {/* <h2 className="text-2xl font-mono font-bold mb-6 text-white">
              Try Our Interactive Python Editor
              </h2> */}
              <HomePageDemo />
            </div>
          </div>
        ) : (
          <div className="space-y-12">
            <AvailableCourses />

            <div className="hidden">
              <ProtectedAdmin>
                <div className="border-t border-gray-200/10 dark:border-gray-700/50 pt-8">
                  <h3 className="text-2xl font-semibold mb-8 text-white/90">
                    Admin Controls
                  </h3>
                  <div className="flex gap-6">
                    <Link
                      href="/admin/reports"
                      className="inline-flex items-center px-6 py-3 rounded-lg shadow-lg text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <BarChart className="h-5 w-5 mr-3" />
                      View Reports
                    </Link>
                    <Link
                      href="/admin/add/lesson"
                      className="inline-flex items-center px-6 py-3 rounded-lg shadow-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <PlusCircle className="h-5 w-5 mr-3" />
                      Add Lesson
                    </Link>
                  </div>
                </div>
              </ProtectedAdmin>
            </div>

            <ProtectedAdmin>
              <div className="border-t border-gray-200/10 dark:border-gray-700/50 pt-8">
                <Link
                  href="/admin"
                  className="inline-flex items-center px-6 py-3 rounded-lg shadow-lg text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Admin
                </Link>
              </div>
            </ProtectedAdmin>
          </div>
        )}
      </div>
    </div>
  );
}
